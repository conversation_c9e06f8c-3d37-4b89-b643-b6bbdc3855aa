/**
 * 购物车数量更新优化测试脚本
 * 在浏览器控制台中运行此脚本来验证优化效果
 */

// 测试配置
const TEST_CONFIG = {
  CLICK_INTERVAL: 50, // 快速点击间隔（毫秒）
  CLICK_COUNT: 5,     // 点击次数
  TEST_TIMEOUT: 5000  // 测试超时时间
};

/**
 * 模拟快速点击测试
 */
function testRapidClicks() {
  console.log('🧪 开始快速点击测试...');
  
  // 记录测试开始时间
  const startTime = Date.now();
  
  // 获取第一个商品的加号按钮
  const plusButtons = document.querySelectorAll('.u-number-box__plus');
  if (plusButtons.length === 0) {
    console.error('❌ 未找到商品数量增加按钮');
    return;
  }
  
  const firstPlusButton = plusButtons[0];
  console.log('📍 找到测试按钮:', firstPlusButton);
  
  // 监听网络请求
  const originalFetch = window.fetch;
  let requestCount = 0;
  const requests = [];
  
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('updateBuyNum')) {
      requestCount++;
      requests.push({
        url,
        timestamp: Date.now() - startTime,
        args: args
      });
      console.log(`📡 API请求 #${requestCount}:`, url, `(+${Date.now() - startTime}ms)`);
    }
    return originalFetch.apply(this, args);
  };
  
  // 执行快速点击
  console.log(`⚡ 开始快速点击 ${TEST_CONFIG.CLICK_COUNT} 次，间隔 ${TEST_CONFIG.CLICK_INTERVAL}ms`);
  
  for (let i = 0; i < TEST_CONFIG.CLICK_COUNT; i++) {
    setTimeout(() => {
      console.log(`👆 点击 #${i + 1}`);
      firstPlusButton.click();
    }, i * TEST_CONFIG.CLICK_INTERVAL);
  }
  
  // 等待测试完成
  setTimeout(() => {
    // 恢复原始fetch
    window.fetch = originalFetch;
    
    // 输出测试结果
    console.log('\n📊 测试结果:');
    console.log(`⏱️  测试时长: ${Date.now() - startTime}ms`);
    console.log(`👆 点击次数: ${TEST_CONFIG.CLICK_COUNT}`);
    console.log(`📡 API请求次数: ${requestCount}`);
    
    if (requestCount === 1) {
      console.log('✅ 测试通过！成功防止了重复请求');
    } else if (requestCount === 0) {
      console.log('⚠️  警告：没有检测到任何API请求');
    } else {
      console.log('❌ 测试失败！仍然存在重复请求');
      console.log('📋 请求详情:', requests);
    }
    
    // 检查状态
    if (window.vue && window.vue.$data) {
      const vueData = window.vue.$data;
      console.log('\n🔍 Vue状态检查:');
      console.log('updatingCartItems:', vueData.updatingCartItems);
      console.log('updateBuyNumTimers size:', vueData.updateBuyNumTimers?.size || 0);
      console.log('pendingUpdateRequests size:', vueData.pendingUpdateRequests?.size || 0);
    }
    
  }, TEST_CONFIG.TEST_TIMEOUT);
}

/**
 * 测试多商品并发更新
 */
function testMultipleItems() {
  console.log('🧪 开始多商品并发测试...');
  
  const plusButtons = document.querySelectorAll('.u-number-box__plus');
  if (plusButtons.length < 2) {
    console.error('❌ 需要至少2个商品进行并发测试');
    return;
  }
  
  // 监听网络请求
  const originalFetch = window.fetch;
  let requestCount = 0;
  
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('updateBuyNum')) {
      requestCount++;
      console.log(`📡 API请求 #${requestCount}:`, url);
    }
    return originalFetch.apply(this, args);
  };
  
  // 同时点击多个商品
  console.log('⚡ 同时点击多个商品的增加按钮');
  plusButtons[0].click();
  plusButtons[1].click();
  if (plusButtons[2]) plusButtons[2].click();
  
  setTimeout(() => {
    window.fetch = originalFetch;
    console.log(`📊 多商品测试结果: ${requestCount} 个API请求`);
    console.log(requestCount >= 2 ? '✅ 多商品独立处理正常' : '❌ 多商品处理异常');
  }, 2000);
}

/**
 * 检查当前页面状态
 */
function checkCurrentState() {
  console.log('🔍 检查当前页面状态...');
  
  // 检查Vue实例
  const vueInstance = document.querySelector('#app').__vue__;
  if (vueInstance) {
    console.log('✅ Vue实例找到');
    console.log('updatingCartItems:', vueInstance.updatingCartItems);
    console.log('updateBuyNumTimers:', vueInstance.updateBuyNumTimers);
    console.log('pendingUpdateRequests:', vueInstance.pendingUpdateRequests);
  } else {
    console.log('❌ 未找到Vue实例');
  }
  
  // 检查商品数量组件
  const numberBoxes = document.querySelectorAll('.u-number-box');
  console.log(`📦 找到 ${numberBoxes.length} 个数量选择组件`);
  
  // 检查加号按钮
  const plusButtons = document.querySelectorAll('.u-number-box__plus');
  console.log(`➕ 找到 ${plusButtons.length} 个增加按钮`);
}

// 导出测试函数
window.cartOptimizationTest = {
  testRapidClicks,
  testMultipleItems,
  checkCurrentState,
  
  // 运行所有测试
  runAllTests() {
    console.log('🚀 开始运行所有购物车优化测试...\n');
    
    checkCurrentState();
    
    setTimeout(() => {
      testRapidClicks();
    }, 1000);
    
    setTimeout(() => {
      testMultipleItems();
    }, 8000);
  }
};

console.log('📋 购物车优化测试脚本已加载');
console.log('💡 使用方法:');
console.log('  cartOptimizationTest.runAllTests() - 运行所有测试');
console.log('  cartOptimizationTest.testRapidClicks() - 测试快速点击');
console.log('  cartOptimizationTest.testMultipleItems() - 测试多商品并发');
console.log('  cartOptimizationTest.checkCurrentState() - 检查当前状态');
