# 购物车数量更新优化测试

## 优化前的问题
1. 用户快速点击"+"按钮时，会发送多次API请求
2. 每次点击产生不同的requestKey（包含buyNum），导致防重复机制失效
3. 立即修改前端totalMoney可能导致数据不一致

## 优化方案
1. **修改requestKey生成逻辑**：只基于cartId，不包含buyNum
2. **移除立即数据修改**：删除clacAdd和clacSusubtract中的totalMoney立即修改
3. **增强防重复检查**：在updateBuyNumWithDebounce中提前检查是否有进行中的请求

## 具体修改内容

### 1. clacAdd方法优化
- 移除：`this.goodsDataAll.totalMoney = this.$NP.plus(...)`
- 保留：参数验证、边界检查、防抖调用

### 2. clacSusubtract方法优化  
- 移除：`this.goodsDataAll.totalMoney = this.$NP.minus(...)`
- 保留：参数验证、边界检查、防抖调用

### 3. updateBuyNumWithDebounce方法优化
- 新增：提前检查是否有进行中的请求
- 修改：requestKey只基于cartId

### 4. updateBuyNum方法优化
- 修改：requestKey生成逻辑，移除buyNum

## 测试场景
1. **快速连续点击测试**：快速点击"+"按钮3-5次，验证只发送一次API请求
2. **不同商品并发测试**：同时操作多个商品的数量，验证每个商品独立处理
3. **数据一致性测试**：验证前端显示与后端数据保持一致

## 预期效果
1. 无论用户点击多快，每个商品同时只会有一个更新请求
2. 前端数据完全依赖后端API返回，确保数据一致性
3. 用户体验保持流畅，没有明显的延迟或卡顿
4. 减少服务器压力，避免无效的重复请求

## 验证方法
1. 打开浏览器开发者工具的Network面板
2. 快速点击商品的"+"按钮
3. 观察API请求数量，应该只有一次updateBuyNum请求
4. 验证购物车数据更新正确
