<template>
	<view :class="['qn-page-' + theme]">
		<view class="container">
			<Aempty text="您购物车暂无商品，快去加购吧～" src="https://img-public.hui1688.cn/car.png" v-if="!isLogin || (invalidData.length === 0 && goodsData.length === 0)"></Aempty>
			<view v-else>
				<!-- 列表 goodsData -->
				<view class="card" v-for="(item, index) in goodsData" :key="index">
					<view class="box clearfix">
						<text
							@click="checkShop(item.checkedShop, item.shopId, 2, item)"
							class="ibonfont"
							:class="[item.checkedShop ? 'ibonxuanze_xuanzhong primary-color' : 'ibonxuanze']"
						></text>
						<text class="title">{{ item.shopName }}</text>
					<view v-if="shopFullBuyMap[item.shopId]" class="full-buy-info primary-color">
						<view class="full-buy-info-left">
							<view class="full-buy-brand primary-btn-pain">换购</view>
							<view>
							<view class="full-buy-msg">
								<text>{{ shopFullBuyMsgMap[item.shopId].msg }}</text>
							</view>
							<text v-if="shopFullBuyMap[item.shopId].startTime > currentTime" class="full-buy-countdown">
								{{ formatCountdown(shopFullBuyMap[item.shopId].startTime - currentTime) }} 后开始
							</text>
							<text v-else-if="shopFullBuyMap[item.shopId].endTime > currentTime" class="full-buy-countdown">
								{{ formatCountdown(shopFullBuyMap[item.shopId].endTime - currentTime) }} 后结束
							</text>
							</view>
						</view>
						<view class="full-buy-btn">
							<text v-if="shopFullBuyMsgMap[item.shopId].needFullBuy" style="color: #808080FF;" >去换购</text>
							<text v-else @click="openFullBuyGoods(item.shopId)">去换购</text>
							<u-icon name="arrow-right" size="20"></u-icon>
						</view>
						</view>
					</view>
					<view class="cart-list">
						<u-swipe-action
							class="uniSwipe"
							:options="options"
							v-for="(goods, goodsindex) in item.shopGoodsData"
							:key="goodsindex"
							:show="goods.isShow"
							:index="goodsindex"
							@open="openItem(index, goodsindex)"
							@click="deleteCartItem([goods.cartId], goods)"
						>
							<view class="cart-item clearfix">
								<!-- 是否选中 -->
								<text
									v-if="goods.sourceType == 3"
									class="ibonfont float_left"
								></text>
								<text
									v-else
									@click="checkShop(goods.selection === 5, goods.cartId, 1, goods, item)"
									class="ibonfont float_left"
									:class="[goods.selection === 5 ? 'ibonxuanze_xuanzhong primary-color' : 'ibonxuanze']"
								></text>
								<view class="image-wrapper float_left" @click="goPage(`/pagesT/product/product?id=${goods.goodsId}&skuId=${goods.skuId}`)">
									<!-- 图片 -->
									<image :src="goods.goodsImages" mode="aspectFill" />
								</view>
								<!-- 标题 -->
								<view class="item-right float_left">

									<text class="title" @click="goPage(`/pagesT/product/product?id=${goods.goodsId}&skuId=${goods.skuId}`)">
										{{ $tt(goods.goodsName) }}
										<text v-if="goods.sourceType == 3" class="full-buy-brand primary-btn-pain">换购</text>
									</text>
									<view class="attr" @click="goPage(`/pagesT/product/product?id=${goods.goodsId}&skuId=${goods.skuId}`)">
										<block v-if="goods.specGroup && goods.specGroup.length">
											<text>{{ $tt('规格') }}：</text>
											<text v-for="(sku, skui) in goods.specGroup" :key="skui" style="padding-right: 4upx;">{{ $tt(sku.specValueName) }};</text>
										</block>
										<!--仓库-->
										<view v-if="goods.warehouseName" class="attr">{{ $tt('仓库') }}：{{ goods.warehouseName }}</view>
									</view>
									<view v-if="goods.sourceType != 2" class="BotStyle clearfix">
										<view class="price float_left primary-color">
											<text class="icon-rmb">¥</text>
											<view style="display: inline-block;"><rich-text :nodes="$_utils.splitPrice(goods.price)"></rich-text></view>
											<text style="font-size: 20rpx;">/{{ $tt(goods.unitName) }}</text>
										</view>
										<view v-if="goods.isActivity === 5" class="activity-tag float_left primary-bg">{{ $tt('活动价') }}</view>
										<!-- 满赠按钮从这里移除 -->
										<view class="float_right clearfix number-box">
											<block v-if="goods.isDistribution === 5">
												<!-- 开启预售不验证最大值 -->
												<u-number-box
													:bg-color="primaryColor"
													:value="goods.buyNum"
													:input-height="44"
													:min="goods.setNum"
													:step="goods.stepNum"
													:disabled="disabledNumberInput"
													@blur="numberChange($event, goods)"
													@plus="clacAdd($event, goods)"
													@minus="clacSusubtract($event, goods)"
												></u-number-box>
											</block>
											<block v-else>
												<!-- 未开启预售最大值为库存数 -->
												<u-number-box
													:bg-color="primaryColor"
													:value="goods.buyNum"
													:input-height="44"
													:max="Number(goods.limitQuantity)"
													:min="goods.setNum"
													:step="goods.stepNum"
													:disabled="disabledNumberInput"
													@blur="numberChange($event, goods)"
													@plus="clacAdd($event, goods)"
													@minus="clacSusubtract($event, goods)"
												></u-number-box>
											</block>
										</view>
									</view>
									<!-- 满赠提示放在价格下面 -->
									<view v-if="itemFullGives[goods.skuId] && itemFullGives[goods.skuId].length" 
										class="full-give-info">
										<view 
											class="full-give-btn" 
											:class="{ 'primary-bg': hasItemFullGiveCondition(goods) }"
											@click.stop="showItemFullGive(goods)">
											<template v-if="hasItemFullGiveCondition(goods)">
												<template v-if="hasSelectedGift(goods)">
													{{ $tt('已选赠品') }}
												</template>
												<template v-else>
													{{ $tt('去选赠品') }}
												</template>
											</template>
											<template v-else>
												<text class="full-give-text">{{ $tt('满赠') }}</text>
												<text v-if="itemFullGives[goods.skuId]" class="full-give-tip">
													{{ getItemFullGiveGapText(goods) }}
												</text>
											</template>
										</view>
									</view>
									<view v-if="goods.sourceType == 2" class="BotStyle clearfix">
										<view class="price float_left primary-color">
											<view class="icon-rmb" style="color: black">x{{goods.buyNum}} {{ goods.unitName }}</view>
										</view>
										<view class="float_right clearfix number-box">
											<text class="primary-color">{{ $tt('赠品') }}</text>
										</view>
									</view>
								</view>
							</view>
						</u-swipe-action>
					</view>
				</view>
				<view class="card invalid-card" v-if="invalidData.length">
					<view class="clearfix invalid-view">
						<text class="float_left invalid-tit">{{ $tt('失效商品') }}（{{ invalidData.length }}）</text>
						<text class="float_right invalid-btn primary-color" @click="clearInvalid">{{ $tt('清空失效商品') }}</text>
					</view>
					<view class="cart-list" v-for="(goods, goodsindex) in invalidData" :key="goodsindex">
						<view class="cart-item clearfix">
							<!-- 是否选中 -->
							<view class="image-wrapper float_left">
								<view class="invalid-tag"><text>{{ $tt('已失效') }}</text></view>
								<!-- 图片 -->
								<image :src="goods.goodsImages" mode="aspectFill" />
							</view>
							<!-- 标题 -->
							<view class="item-right float_left">
								<view class="title ellipsis">{{ $tt(goods.goodsName) }}</view>
								<view class="invalid-msg">{{ goods.invalidMsg }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 优惠券面板 -->
		</view>
		<scroll-view :scroll-x="true" class="full-give-list" v-if="isLogin && fullGive_list.length > 0">
			<view v-if="isLogin && !fullGive_list.length && goodsDataAll.totalMoney > 0" class="full-give-loading">
				<u-loading mode="circle" color="var(--primary-color)" size="24"></u-loading>
				<text>加载满赠活动...</text>
			</view>
			<view v-for="(item, index) in fullGive_list" :key="index" class="full-give-item" @click="handleFullGiveSel(item)">
				<view class="full-give-price">
					<!-- 金额满赠 -->
					<view v-if="!item.giftType || item.giftType === 1" class="full-give-full">{{ $tt('满') + item.requiredAmount + $tt('元赠') }}</view>
					<!-- 数量满赠 -->
					<view v-else-if="item.giftType === 2" class="full-give-full">
						<template v-if="item.targetSkus && item.targetSkus.length > 0">
							{{ $tt('指定商品满') + item.requiredQuantity + $tt('件赠') }}
							<text v-if="item.isMultipleGift && item.giftMultiple > 1" class="multiple-gift-tip">
								({{ $tt('可得') }}{{ item.giftMultiple }}{{ $tt('倍赠品') }})
							</text>
						</template>
						<template v-else>
							{{ $tt('满') + item.requiredQuantity + $tt('件赠') }}
							<text v-if="item.isMultipleGift && item.giftMultiple > 1" class="multiple-gift-tip">
								({{ $tt('可得') }}{{ item.giftMultiple }}{{ $tt('倍赠品') }})
							</text>
						</template>
					</view>
					
					<!-- 金额满赠 - 进度条显示 -->
					<view v-if="!item.giftType || item.giftType === 1" class="full-give-balance">
						<template v-if="item.balance > 0">
							{{$tt('再购买')}}¥{{ item.balance }}
						</template>
						<template v-else>
							{{$tt('已满足条件')}}
						</template>
					</view>
					
					<!-- 数量满赠 - 进度条显示 -->
					<view v-else-if="item.giftType === 2" class="full-give-balance">
						<template v-if="item.balance > 0">
							{{$tt('再购买')}}{{ item.balance }}{{$tt('件')}}
						</template>
						<template v-else>
							{{$tt('已满足条件')}}
						</template>
					</view>
				</view>
				<view class="full-give-img">
					<image
						mode="aspectFill"
						class="full-give-img-one"
						:src="getFullGiveImage(item)"
						@error="handleImageError"
					></image>
				</view>
			</view>	
		</scroll-view>
		<!-- 底部菜单栏 -->
		<view class="action-section safe-area-inset-bottom" v-if="isLogin">
			<view class="action-section-main">
				<view class="checkbox clearfix">
					<text @click="checkShop(allChecked, '', 3)" class="ibonfont float_left" :class="[allChecked ? 'ibonxuanze_xuanzhong primary-color' : 'ibonxuanze']"></text>
					<view class="all-btn float_right" :class="{ show: allChecked }">
						<text @click="checkShop(allChecked, '', 3)">{{ $tt('全选') }}</text>
						<!-- #ifdef MP -->
						<text style="margin-left: 24upx;" @click="is_edit = !is_edit">{{ is_edit ? $tt('完成') : $tt('编辑') }}</text>
						<!-- #endif -->
					</view>
				</view>
				<view class="total-box" v-if="!is_edit">
					<view>
						<text class="total-text">{{ $tt('合计') }}：</text>
						<text class="price primary-color">¥{{ $_utils.formatNub(goodsDataAll.payMoney) || 0 }}</text>
					</view>
					<!-- 说明 -->
					<view class="freeExpressPrice" v-if="!goodsDataAll.totalMoney && startDeliveryPrice" @click="goPage('/pages/classification/cate', 'switchTab')">
						<text>满{{ startDeliveryPrice }}元起送，先去逛逛</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
					<view
						class="freeExpressPrice"
						v-else-if="startDeliveryPrice && goodsDataAll.totalMoney < startDeliveryPrice"
						@click="goPage('/pages/classification/cate', 'switchTab')"
					>
						<text>还差{{ $NP.minus(startDeliveryPrice, goodsDataAll.totalMoney) }}元起送，再去逛逛</text>
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>

					<view class="freeExpressPrice" v-else-if="freeExpressPrice > 0" @click="goPage('/pages/classification/cate', 'switchTab')">
						还差{{ freeExpressPrice.toFixed(2) }}元免运费，去凑单
						<u-icon name="arrow-right" size="20"></u-icon>
					</view>
					<!--  v-else-if="goodsDataAll.preferential > 0"  -->
					<view class="freeExpressPrice" v-else @click="goPage('/pages/classification/cate', 'switchTab')">
						总额：¥{{ goodsDataAll.totalMoney || 0 }} 活动优惠：-¥{{ goodsDataAll.preferential || 0 }}
					</view>
				</view>
				<view v-if="!is_edit" @click="createOrder" class="confirm-btn primary-bg">
					<text v-if="!goodsDataAll.totalMoney">{{ $tt('随便逛逛') }}</text>
					<text v-else-if="goodsDataAll.totalMoney < startDeliveryPrice">{{ $tt('去凑单') }}</text>
					<text v-else>{{ $tt('去结算') }}</text>
					<!--  ({{ goodsDataAll.checkNum || 0 }}) -->
				</view>
				<text v-if="is_edit" class="del-goods-btn" @click="delSelect">{{ $tt('删除') }}</text>
			</view>
		</view>

		<!--满赠商品列表-->
		<u-popup
			v-model="fullGiveGoods_show"
			:mask-close-able="true"
			mode="bottom"
			height="800rpx"
			:border-radius="24"
			@close="fullGiveGoods_show = false">
			<view class="full-give-title">
				<text>满赠商品</text>
				<text class="full-give-close" @click="fullGiveGoods_show = false">关闭</text>
			</view>
			<scroll-view :scroll-y="true" class="full-give-goods-scroll">
				<view class="full-give-goods-list">
					<view v-if="fullGiveInfo && fullGiveInfo.availableLevel" class="full-give-level-info">
						<text class="level-title">{{ fullGiveInfo.title }}</text>
						<text class="level-desc">
							<text v-if="!fullGiveInfo.availableLevel.giftType || fullGiveInfo.availableLevel.giftType === 1">
								<!-- 金额满赠 -->
								<template v-if="fullGiveInfo.availableLevel.targetType === 1">
									满{{ fullGiveInfo.availableLevel.requiredAmount }}元可选赠品
								</template>
								<template v-else>
									购买指定商品满{{ fullGiveInfo.availableLevel.requiredAmount }}元可选赠品
								</template>
							</text>
							<text v-else-if="fullGiveInfo.availableLevel.giftType === 2">
								<!-- 数量满赠 -->
								<template v-if="fullGiveInfo.availableLevel.targetSkus && fullGiveInfo.availableLevel.targetSkus.length > 0">
									购买指定商品满{{ fullGiveInfo.availableLevel.requiredQuantity }}件可选赠品
									<text v-if="fullGiveInfo.isMultipleGift && fullGiveInfo.availableLevel.giftMultiple > 1" class="multiple-gift-tip">
										({{ $tt('可选') }}{{ fullGiveInfo.availableLevel.giftMultiple }}{{ $tt('倍赠品') }})
									</text>
								</template>
								<template v-else>
									商品满{{ fullGiveInfo.availableLevel.requiredQuantity }}件可选赠品
									<text v-if="fullGiveInfo.isMultipleGift && fullGiveInfo.availableLevel.giftMultiple > 1" class="multiple-gift-tip">
										({{ $tt('可选') }}{{ fullGiveInfo.availableLevel.giftMultiple }}{{ $tt('倍赠品') }})
									</text>
								</template>
								<text class="remaining-gift-tip">
									（剩余可选：{{ getRemainingGiftCount(fullGiveInfo) }}件）
								</text>
							</text>
						</text>
					</view>
					
					<!-- 加载状态 -->
					<view v-if="fullGiveGiftsLoading" class="full-give-loading">
						<u-loading mode="circle" size="36"></u-loading>
						<text>正在加载赠品列表...</text>
					</view>
					
					<!-- 赠品列表 -->
					<block v-else>
						<view class="full-give-goods-item" v-for="(item, index) in fullGiveGiftsList" :key="index">
							<view class="full-give-goods-image">
								<image
									mode="aspectFill"
									:src="getGoodsImage(item)"
									@error="handleImageError"
								></image>
							</view>
							<view class="full-give-goods-info">
								<view class="full-give-goods-title">{{ getGoodsTitle(item) }}</view>
								<view class="full-give-goods-spec" v-if="getGoodsSpec(item)">
									<text>规格：</text>
									<text>{{ getGoodsSpec(item) }}</text>
								</view>
								<view class="full-give-goods-stock">库存：{{ getGoodsStock(item) }}{{ getGoodsUnit(item) }}</view>
							</view>
							<view class="full-give-goods-action">
								<view v-if="fullGiveInfo.isMultipleGift && fullGiveInfo.availableLevel && fullGiveInfo.availableLevel.giftMultiple > 1"
									  class="multiple-gift-quantity">
									可得{{ fullGiveInfo.availableLevel.giftMultiple }}件
								</view>
								<view class="full-give-goods-btn" @click="handleFullGiveGoodsSel(item)">
									<text>选择</text>
								</view>
							</view>
						</view>
						
						<!-- 空状态 -->
						<view v-if="!fullGiveGiftsList.length" class="full-give-empty">
							<text>暂无可选赠品</text>
							<view class="retry-btn" @click="loadFullGiveGifts">重新加载</view>
						</view>
					</block>
				</view>
			</scroll-view>
		</u-popup>
		<u-popup
			v-model="fullBuyGoods_show"
			:border-radius="40"
			:mask-close-able="true"
			mode="bottom"
			height="70%"
			@close="fullBuyGoods_show = false">
			<view class="full-buy-title">
				<text>满赠商品</text>
				<text class="full-buy-close" @click="fullBuyGoods_show = false">关闭</text>
			</view>
			<scroll-view :scroll-y="true" class="full-buy-goods-scroll">
				<view v-for="(item, index) in fullBuyGoods" :key="index">
					<view class="cart-item clearfix full-buy-goods-item">
						<!-- 左侧 -->
						<view class="image-wrapper float_left">
							<image :src="item.specImage || item.images[0]" mode="aspectFill"></image>
						</view>
						<!-- 右侧 -->
						<view class="item-right float_left full-buy-goods-item-right">
							<text class="title">{{ item.title }}</text>
							<view class="attr">
								<block>
									<text>{{ $tt('规格') }}</text>
									<text>{{ item.specData.map(attr => attr.specValueName).join(';') }}</text>
									<view v-if="item.limitQuantity" class="limit-quantity">限购{{item.limitQuantity}}{{ $tt(item.unitName) }}</view>
								</block>
							</view>
							<view class="BotStyle clearfix">
								<view class="price float_left primary-color">
									<text class="icon-rmb">¥</text>
									<view style="display: inline-block;"><rich-text :nodes="$_utils.splitPrice(item.exchangePrice)"></rich-text></view>
									<text style="font-size: 20rpx;">/{{ $tt(item.unitName) }}</text>
								</view>
								<view class="float_right clearfix number-box">
									<text v-if="item.buyNum === 0" class="primary-color" @click="handleFullBuyGoodsAdd(item)">{{ $tt('加入购物车') }}</text>
									<block v-else>
										<u-number-box v-model="item.buyNum" :bg-color="primaryColor" :input-height="44" :min="0" :max="item.limitQuantity">

										</u-number-box>
									</block>
								</view>
							</view>

						</view>
					</view>
				</view>
			</scroll-view>
			<view class="full-buy-footer">
				<view class="full-buy-footer-btn float_right primary-bg" @click="handleFullBuyGoodsConfirm">确定</view>
			</view>
		</u-popup>
		<!-- 底部tabbar -->
		<Tabbar v-model="tab_current"></Tabbar>

    	<NoLoginTip @cancel="cancelTip" :show="loginTip" v-if="loginTip"/>

		<!-- 添加满赠活动弹窗 -->
		<u-popup
			v-model="itemFullGiveShow"
			:mask-close-able="true"
			mode="bottom"
			height="800rpx"
			:border-radius="24"
			@close="itemFullGiveShow = false">
			<view class="full-give-title">
				<text>{{ $tt('满赠商品') }}</text>
				<text class="full-give-close" @click="itemFullGiveShow = false">{{ $tt('关闭') }}</text>
			</view>
			<scroll-view :scroll-y="true" class="full-give-goods-scroll">
				<view class="full-give-goods-list">
					<!-- 满赠活动信息 -->
					<view v-if="currentItemFullGive" class="full-give-level-info">
						<text class="level-title">{{ currentItemFullGive.title }}</text>
						<text class="level-desc">
							<!-- 金额满赠 -->
							<template v-if="currentItemFullGive.giftType === 1 && currentItemFullGive.targetType === 2">
								{{ $tt('购买指定商品满') }}{{ currentItemFullGive.requiredAmount }}{{ $tt('元可选赠品') }}
								<text v-if="currentItemFullGive.balance > 0" class="primary-color">
									({{ $tt('再购买') }}¥{{ currentItemFullGive.balance }})
								</text>
							</template>
							<!-- 数量满赠 -->
							<template v-else-if="currentItemFullGive.giftType === 2">
								{{ $tt('购买指定商品满') }}{{ currentItemFullGive.requiredQuantity }}{{ $tt('件可选赠品') }}
								<text v-if="currentItemFullGive.isMultipleGift && currentItemFullGive.giftMultiple > 1" class="multiple-gift-tip">
									({{ $tt('可选') }}{{ currentItemFullGive.giftMultiple }}{{ $tt('倍赠品') }})
								</text>
								<text class="remaining-gift-tip">
									（剩余可选：{{ getRemainingGiftCount(currentItemFullGive) }}件）
								</text>
								<text v-if="currentItemFullGive.balance > 0" class="primary-color">
									({{ $tt('再购买') }}{{ currentItemFullGive.balance }}{{ $tt('件') }})
								</text>
							</template>
						</text>
					</view>
					
					<!-- 条件不满足时显示提示 -->
					<view v-if="currentItemFullGive && !hasItemFullGiveCondition(currentItemFullGive.currentGoods)" class="full-give-not-meet">
						<text>{{ getFullGiveConditionDetail(currentItemFullGive.currentGoods).message || $tt('您当前选择的商品数量不满足满赠条件') }}</text>
						<view class="primary-bg add-more-btn" @click="addMoreForFullGive">{{ $tt('增加购买数量') }}</view>
					</view>
					
					<!-- 满足条件时显示赠品列表 -->
					<block v-else>
						<!-- 赠品列表 -->
						<view class="full-give-goods-item" v-for="(item, index) in currentItemFullGiveGifts" :key="index">
							<view class="full-give-goods-image">
								<image
									mode="aspectFill"
									:src="getGoodsImage(item)"
									@error="handleImageError"
								></image>
							</view>
							<view class="full-give-goods-info">
								<view class="full-give-goods-title">{{ getGoodsTitle(item) }}</view>
								<view class="full-give-goods-spec" v-if="getGoodsSpec(item)">
									<text>{{ $tt('规格') }}：</text>
									<text>{{ getGoodsSpec(item) }}</text>
								</view>
								<view class="full-give-goods-stock">{{ $tt('库存') }}：{{ getGoodsStock(item) }}{{ getGoodsUnit(item) }}</view>
							</view>
							<view class="full-give-goods-action">
								<view class="full-give-goods-btn"
									:class="hasItemFullGiveCondition(currentItemFullGive.currentGoods) ? 'primary-bg' : 'disabled-btn'"
									@click="hasItemFullGiveCondition(currentItemFullGive.currentGoods) ? handleItemFullGiveGoodsSel(item) : handleDisabledGiftClick()">
									<text v-if="hasItemFullGiveCondition(currentItemFullGive.currentGoods)">{{ $tt('选择') }}</text>
									<text v-else>{{ $tt('条件不满足') }}</text>
								</view>
							</view>
						</view>
						
						<!-- 空状态 -->
						<view v-if="!currentItemFullGiveGifts.length" class="full-give-empty">
							<text>{{ $tt('暂无可选赠品') }}</text>
						</view>
					</block>
				</view>
			</scroll-view>
		</u-popup>
	</view>
</template>
<script>
import GoodsItem from "@/components/GoodsItem.vue";
import Login from '@/components/Login.vue';
import NoLoginTip from "@/components/NoLoginTip.vue";
import Blank from "@/components/PageDesign/components/Blank.vue";
export default {
	components: {
		Blank,
		NoLoginTip,
		Login,
		GoodsItem
	},
	data() {
		return {
			tab_current: 2,
			is_edit: false,
			totalMoney: 0, //总价格
			buyNum: 1, //更新购物数字
			checkedShop: false, //商铺选择
			allChecked: false, //全选状态  true|false
			empty: false, //空白页现实  true|false
			goodsData: [], //店铺列表
			invalidData: [], //失效商品
			shopGoodsData: [], //商品列表
			options: [
				{
					text: '删除',
					style: {
						backgroundColor: 'rgb(255,58,49)'
					}
				}
			],
			goodsDataAll: {
				activityMoney: '0.00',
				cartNum: 0,
				checkNum: 0,
				expressMoney: 0,
				goodsData: [],
				goodsNum: 0,
				invalidData: [],
				payMoney: '0.00',
				preferential: '0.00',
				totalMoney: '0.00',
				vipDiscount: 0,
				vipDoubleDiscount: 0
			},
			fullGive_list: [],
			fullGiveGoods_show: false,
			fullGiveItem: {},
			fullBuy_list: [],
			loginTip: false,
			hasAddFullGive: false,
			disabledNumberInput: false,
			currentTime: 0,
			timer: null,
			fullBuyGoods_show: false,
			fullBuyGoods: [],
			fullGiveInfo: null, // 满赠活动信息
			fullGiveGiftsList: [], // 满赠赠品列表
			fullGiveLoading: false,  // 满赠活动加载状态
			fullGiveGiftsLoading: false,  // 赠品列表加载状态
			itemFullGives: {}, // 商品skuId到对应满赠活动的映射
			currentItemFullGive: null, // 当前选中的商品满赠活动
			itemFullGiveShow: false, // 商品满赠活动弹窗显示状态
			currentItemFullGiveGifts: [], // 当前商品满赠活动的赠品列表
			selectedGiftMap: {}, // 已选择的赠品映射 {商品skuId: {满赠活动ID: {赠品skuId: true}}} 用于判断每个满赠活动的赠品数量是否达到上限
			refreshFullGiveTimer: null, // 满赠活动信息刷新定时器

			// 请求去重和状态管理
			pendingUpdateRequests: new Map(), // 正在进行的更新请求 key: cartId_buyNum, value: Promise
			isUpdatingCart: false, // 是否正在更新购物车（用于控制watch触发）
			updateBuyNumTimer: null, // 数量更新防抖定时器
			getCartTimer: null, // 购物车数据获取防抖定时器
		};
	},
	// #ifdef APP-PLUS || H5
	onNavigationBarButtonTap(obj) {
		var currentWebview = this.$mp.page.$getAppWebview();
		var tn = currentWebview.getStyle().titleNView;
		// 修改buttons
		// index: 按钮索引, style {WebviewTitleNViewButtonStyles }
		if (!this.is_edit) {
			this.is_edit = true;
			tn.buttons[0].text = '完成'; //[0] 按钮的下标
			currentWebview.setStyle({
				titleNView: tn
			});
		} else if (this.is_edit) {
			this.is_edit = false;
			tn.buttons[0].text = '编辑'; //[0] 按钮的下标
			currentWebview.setStyle({
				titleNView: tn
			});
		}
	},
	// #endif

	watch: {
		'$store.state.locationObj'(val) {
			if (JSON.stringify(val) === '{}') {
				this.getAuthorizeInfo();
				return;
			}
			if (this.isLogin) {
				this.getCartByUserCenterId();
			}
		},
		isLogin(val) {
			if (val) {
				this.getCartByUserCenterId();
			}
		},
		goodsAndFullBuyData() {
			const {goodsData, fullBuy_list} = this;
			if (!goodsData.length || !fullBuy_list.length) return;

			goodsData.forEach(goods => {
				const fullBuy = fullBuy_list.find(item => item.shopId === goods.shopId);
				if (!fullBuy) return;

				const shopTotalMoney = this.shopTotalMoneyMap[goods.shopId];
				const fullBuyLevels = fullBuy.fullAmountLevels.filter(level => level.amount <= shopTotalMoney);

				goods.shopGoodsData.forEach(goodsItem => {
					if (goodsItem.sourceType !== 3) return;

					fullBuyLevels.forEach(level => {
						const matchingGoods = level.goods.find(g => g.skuId === goodsItem.skuId);
						if (matchingGoods) {
							goodsItem.fullBuy = matchingGoods;
							goodsItem.limitQuantity = matchingGoods.limitQuantity;
							goodsItem.price = matchingGoods.exchangePrice;
						}
					});
				});
			});
		},
		'goodsDataAll.totalMoney'(newVal, oldVal) {
			// 避免在更新购物车期间重复触发
			if (this.isUpdatingCart) {
				console.log('watch totalMoney: 跳过，正在更新购物车');
				return;
			}

			// 避免初始化时的触发
			if (oldVal === undefined) {
				return;
			}

			// 只有当金额真正发生变化时才触发
			if (newVal !== oldVal) {
				console.log('watch totalMoney: 金额变化，重新计算满赠', { newVal, oldVal });
				// 当总金额变化时，重新计算满赠活动信息
				this.refreshFullGiveInfo();
			}
		},
		// 监听购物车商品数据变化
		'goodsDataAll.goodsData': {
			handler(newVal, oldVal) {
				// 避免在更新购物车期间重复触发
				if (this.isUpdatingCart) {
					console.log('watch goodsData: 跳过，正在更新购物车');
					return;
				}

				// 避免初始化时的触发
				if (!oldVal || oldVal.length === 0) {
					return;
				}

				// 简单的数据变化检测
				if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
					console.log('watch goodsData: 商品数据变化，重新计算满赠');
					// 当购物车商品数据变化时，重新计算满赠活动信息
					this.refreshFullGiveInfo();
				}
			},
			deep: true
		}
	},
	computed: {
		// 判断市场常见的几种刘海屏机型 true为刘海机型
		isBang() {
			return this.$_utils.modelmes();
		},
		isLogin() {
			return this.$store.state.hasLogin;
		},
		startDeliveryPrice() {
			return this.$store.state.baseSet.startDeliveryPrice || 0;
		},
		freeExpressPrice() {
			const freeExpressPrice = this.$store.state.baseSet.freeExpressPrice || 0;
			return this.$NP.minus(freeExpressPrice, this.goodsDataAll.totalMoney);
		},
		shopFullBuyMap() {
			return this.fullBuy_list.reduce((acc, item) => {
				acc[item.shopId] = item;
				return acc;
			}, {});
		},
		shopTotalMoneyMap() {
			return this.goodsData.reduce((acc, item) => {
				item.shopGoodsData.forEach(goods => {
				if (goods.selection === 5) {
					acc[goods.shopId] = this.$NP.plus(Number(acc[goods.shopId] || 0), this.$NP.times(Number(goods.price), Number(goods.buyNum)));
				}
				});
				return acc;
			}, {});
		},
		shopFullBuyMsgMap() {
			const map = {};
			for (let i = 0; i < this.fullBuy_list.length; i++) {
				const item = this.fullBuy_list[i];
				// 如果店铺没有满额优惠等级配置,跳过
				if (!item.fullAmountLevels?.length) continue;

				// 获取当前店铺的订单总金额,默认为0
				const shopTotalMoney = this.shopTotalMoneyMap[item.shopId] || 0;

				// 找到第一个大于当前订单金额的满额等级
				const currentLevel = item.fullAmountLevels.find(level => level.amount > shopTotalMoney);

				if (!currentLevel) {
				// 如果没找到,说明已经满足最高等级
				map[item.shopId] = {
					msg: '已满足换购条件',
					needFullBuy: false
				};
				continue;
				}

				// 计算还差多少钱
				const diffAmount = this.$NP.minus(currentLevel.amount, shopTotalMoney);

				// 如果是第一个等级,显示基础文案
				if (currentLevel === item.fullAmountLevels[0]) {
				map[item.shopId] = {
					msg: `再买${diffAmount}元可换购`,
					needFullBuy: true
				};
				} else {
				// 否则显示可换购更多的文案
				map[item.shopId] = {
					msg: `再买${diffAmount}元可换购更多`,
					needFullBuy: true
				};
				}
			}
			return map;
		},
		goodsAndFullBuyData() {
			const {goodsData, fullBuy_list} = this;
			return {goodsData, fullBuy_list}
		}
	},
	onLoad() {
		if (JSON.stringify(this.$store.state.locationObj) === '{}') {
			this.getAuthorizeInfo();
		}
		// #ifdef MP-WEIXIN
		// 小程序的原生菜单中显示分享按钮
		uni.showShareMenu({
			withShareTicket: false,
			menus: ['shareAppMessage', 'shareTimeline']
		});
		// #endif
		
		// 尝试恢复赠品选择状态
	},

	async onShow() {
		if (this.$store.state.hasLogin) {
			await this.getCartByUserCenterId();
			// 在获取购物车数据后，再获取满赠活动数据
			await this.getAllUsableFullGive();
			await this.getAllUsableFullBuy();
			
			// 尝试恢复赠品选择状态
		}
	},
	onPullDownRefresh() {
		if (this.isLogin) {
			this.getCartByUserCenterId();
			this.getAllUsableFullGive();
			this.getAllUsableFullBuy();
		} else {
			uni.stopPullDownRefresh();
		}
	},
	created() {
		this.startCountdown();
	},
	beforeDestroy() {
		this.clearTimer();
	},
	methods: {
		// 获取满赠活动图片
		getFullGiveImage(item) {
			try {
				// 优先使用第一个商品的第一张图片
				if (item.skus && item.skus.length > 0) {
					const firstSku = item.skus[0];

					if (firstSku.images && firstSku.images.length > 0) {
						return this.ensureImageUrl(firstSku.images[0]);
					}
					// 如果images不存在，尝试使用image字段
					if (firstSku.image) {
						return this.ensureImageUrl(firstSku.image);
					}
					// 如果image不存在，尝试使用goodsImage字段
					if (firstSku.goodsImage) {
						return this.ensureImageUrl(firstSku.goodsImage);
					}
				}

				// 如果没有商品图片，返回默认图片
				return this.getDefaultImage();
			} catch (error) {
				console.error('获取满赠活动图片失败:', error);
				return this.getDefaultImage();
			}
		},

		// 处理图片加载错误
		handleImageError(e) {
			console.warn('满赠活动图片加载失败:', e);
			// 设置默认图片
			e.target.src = this.getDefaultImage();
		},

		// 确保图片URL完整性
		ensureImageUrl(imageUrl) {
			if (!imageUrl) return this.getDefaultImage();

			// 如果已经是完整URL，直接返回
			if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
				return imageUrl;
			}

			// 如果是相对路径，添加域名前缀
			if (imageUrl.startsWith('/')) {
				return 'https://img.hui1688.cn' + imageUrl;
			}

			// 其他情况，添加完整前缀
			return 'https://img.hui1688.cn/' + imageUrl;
		},

		// 获取默认图片
		getDefaultImage() {
			// 使用一个简单的占位图片或者空白图片
			return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWVhuWTgeWbvueJhzwvdGV4dD48L3N2Zz4=';
		},

		// 获取商品图片（用于弹框中的商品显示）
		getGoodsImage(item) {
			try {
				// 优先使用images数组的第一张图片
				if (item.images && item.images.length > 0) {
					return this.ensureImageUrl(item.images[0]);
				}
				// 如果images不存在，尝试使用image字段
				if (item.image) {
					return this.ensureImageUrl(item.image);
				}
				// 如果image不存在，尝试使用goodsImage字段
				if (item.goodsImage) {
					return this.ensureImageUrl(item.goodsImage);
				}

				// 如果没有图片，返回默认图片
				return this.getDefaultImage();
			} catch (error) {
				console.error('获取商品图片失败:', error);
				return this.getDefaultImage();
			}
		},

		// 获取商品标题
		getGoodsTitle(item) {
			return item.title || item.goodsName || item.name || '商品名称';
		},

		// 获取商品规格
		getGoodsSpec(item) {
			try {
				if (item.specGroup && item.specGroup.length > 0) {
					const specs = item.specGroup.map(spec => {
						if (spec.specValueName) {
							return spec.specValueName;
						}
						return spec.specName + ':' + spec.specValueName;
					}).filter(Boolean);
					return specs.join(';');
				}
				return '';
			} catch (error) {
				console.error('获取商品规格失败:', error);
				return '';
			}
		},

		// 获取商品库存
		getGoodsStock(item) {
			return item.inventory || item.stock || 0;
		},

		// 获取商品单位
		getGoodsUnit(item) {
			return item.unitName || '件';
		},

		// 获取满赠活动的剩余可选赠品数量
		getRemainingGiftCount(fullGive) {
			if (!fullGive) return 0;

			// 获取总的赠品上限
			const totalLimit = this.getFullGiveLimitCount(fullGive);

			// 计算已选择的赠品数量
			let selectedCount = 0;
			if (this.goodsDataAll && this.goodsDataAll.goodsData) {
				this.goodsDataAll.goodsData.forEach(shop => {
					shop.shopGoodsData.forEach(cartGoods => {
						if (cartGoods.sourceType === 2 && cartGoods.fullGiveId === fullGive.id) {
							selectedCount += cartGoods.buyNum;
						}
					});
				});
			}

			return Math.max(0, totalLimit - selectedCount);
		},



		// 启动倒计时
		startCountdown() {
			this.timer = setInterval(() => {
				this.currentTime = Math.floor(Date.now() / 1000);
			}, 1000);
		},
		// 清除倒计时
		clearTimer() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
		},
		// 获取满赠活动的赠品上限（基于倍数赠送）
		getFullGiveLimitCount(fullGive) {
			if (!fullGive) return 1; // 默认上限为1

			// 如果启用了倍数赠送，赠品上限等于倍数
			if (fullGive.isMultipleGift == 5 && fullGive.giftMultiple > 1) {
				return fullGive.giftMultiple;
			}

			// 未启用倍数赠送时，默认上限为1
			return 1;
		},
		// 检查商品是否已添加满赠赠品
		hasSelectedGift(goods) {
			if (!goods || !goods.skuId || !this.selectedGiftMap[goods.skuId]) return false;
			
			// 检查是否有任何满赠活动的赠品
			const fullGiveIds = Object.keys(this.selectedGiftMap[goods.skuId] || {});
			for (let i = 0; i < fullGiveIds.length; i++) {
				const fullGiveId = fullGiveIds[i];
				const gifts = this.selectedGiftMap[goods.skuId][fullGiveId] || {};
				if (Object.keys(gifts).length > 0) {
					return true;
				}
			}
			
			return false;
		},
		// 检查商品是否已达到赠品上限
		isGiftLimitReached(goods, fullGive) {
			if (!goods || !goods.skuId || !fullGive || !fullGive.id) return false;

			// 获取满赠活动ID
			const fullGiveId = fullGive.id;

			// 检查该商品的该满赠活动是否已有赠品记录
			if (!this.selectedGiftMap[goods.skuId] || !this.selectedGiftMap[goods.skuId][fullGiveId]) {
				return false;
			}

			// 计算已选择的赠品总数量（从购物车中统计）
			let selectedGiftCount = 0;

			// 遍历购物车，统计该满赠活动的赠品数量
			if (this.goodsDataAll && this.goodsDataAll.goodsData) {
				this.goodsDataAll.goodsData.forEach(shop => {
					shop.shopGoodsData.forEach(cartGoods => {
						// 检查是否是赠品（sourceType = 2）且与当前满赠活动相关
						if (cartGoods.sourceType === 2 && cartGoods.fullGiveId === fullGiveId) {
							selectedGiftCount += cartGoods.buyNum;
						}
					});
				});
			}

			// 获取满赠活动的赠品上限
			const limitCount = this.getFullGiveLimitCount(fullGive);

			// 检查是否达到上限
			return selectedGiftCount >= limitCount;
		},


		// 点击添加clacAdd
		clacAdd(e, goods) {
			// 参数验证
			if (!e || !goods || !goods.cartId || e.value === undefined || e.value === null) {
				console.warn('clacAdd: 无效的参数', e, goods);
				return;
			}

			// 检查数量是否真的发生了变化
			if (e.value === goods.buyNum) {
				return;
			}

			// 边界值检查
			if (e.value < (goods.setNum || 1)) {
				this.$api.msg('商品数量不能小于最小购买数量');
				return;
			}

			if (goods.selection === 5) {
				this.goodsDataAll.totalMoney = this.$NP.plus(Number(this.goodsDataAll.totalMoney), Number(goods.price));
			}

			this.updateBuyNumWithDebounce(goods.cartId, e.value);
		},
		// 点击减少数量
		clacSusubtract(e, goods) {
			// 参数验证
			if (!e || !goods || !goods.cartId || e.value === undefined || e.value === null) {
				console.warn('clacSusubtract: 无效的参数', e, goods);
				return;
			}

			// 检查数量是否真的发生了变化
			if (e.value === goods.buyNum) {
				return;
			}

			// 边界值检查
			if (e.value < (goods.setNum || 1)) {
				this.$api.msg('商品数量不能小于最小购买数量');
				return;
			}

			if (goods.selection === 5) {
				this.goodsDataAll.totalMoney = this.$NP.minus(Number(this.goodsDataAll.totalMoney), Number(goods.price));
			}

			this.updateBuyNumWithDebounce(goods.cartId, e.value);
		},
		// 手动输入
		numberChange(e, goods) {
			// 参数验证
			if (!e || !goods || !goods.cartId || e.value === undefined || e.value === null) {
				console.warn('numberChange: 无效的参数', e, goods);
				return;
			}

			if (e.value <= 0) {
				this.$api.msg('请输入正确的数量');
				return;
			}

			// 检查数量是否真的发生了变化
			if (e.value === goods.buyNum) {
				return;
			}

			// 边界值检查
			if (e.value < (goods.setNum || 1)) {
				this.$api.msg('商品数量不能小于最小购买数量');
				return;
			}

			this.updateBuyNumWithDebounce(goods.cartId, e.value);
		},

		// 防抖版本的购物车数据获取方法
		getCartByUserCenterIdWithDebounce() {
			// 清除之前的定时器
			if (this.getCartTimer) {
				clearTimeout(this.getCartTimer);
			}

			// 生成请求唯一标识
			const requestKey = 'getCartByUserCenterId';

			// 检查是否已有相同的请求在进行中
			if (this.pendingUpdateRequests.has(requestKey)) {
				console.log('getCartByUserCenterId: 请求去重，返回已有Promise');
				return this.pendingUpdateRequests.get(requestKey);
			}

			// 设置新的定时器，200ms后执行
			this.getCartTimer = setTimeout(() => {
				const requestPromise = this.getCartByUserCenterId()
					.finally(() => {
						// 清理请求缓存
						this.pendingUpdateRequests.delete(requestKey);
					});

				// 缓存请求Promise
				this.pendingUpdateRequests.set(requestKey, requestPromise);

				return requestPromise;
			}, 200);

			// 返回一个Promise，在定时器执行后resolve
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					if (this.pendingUpdateRequests.has(requestKey)) {
						this.pendingUpdateRequests.get(requestKey)
							.then(resolve)
							.catch(reject);
					} else {
						resolve();
					}
				}, 200);
			});
		},

		// 防抖版本的数量更新方法
		updateBuyNumWithDebounce(id, buyNum) {
			// 清除之前的定时器
			if (this.updateBuyNumTimer) {
				clearTimeout(this.updateBuyNumTimer);
			}

			// 增加防抖时间到300ms，并立即设置更新状态
			this.isUpdatingCart = true;
			this.disabledNumberInput = true;

			// 设置新的定时器，300ms后执行
			this.updateBuyNumTimer = setTimeout(() => {
				this.updateBuyNum(id, buyNum);
			}, 300);
		},

		// 更新购物车数量 updateBuyNum
		updateBuyNum(id, buyNum) {
			// 参数验证
			if (!id || buyNum === undefined || buyNum === null || buyNum < 0) {
				console.warn('updateBuyNum: 无效的参数', id, buyNum);
				// 重置状态
				this.isUpdatingCart = false;
				this.disabledNumberInput = false;
				return Promise.resolve();
			}

			// 生成请求唯一标识
			const requestKey = `updateBuyNum_${id}_${buyNum}`;

			// 检查是否已有相同的请求在进行中
			if (this.pendingUpdateRequests.has(requestKey)) {
				console.log('updateBuyNum: 请求去重，返回已有Promise', requestKey);
				return this.pendingUpdateRequests.get(requestKey);
			}

			// 确保更新状态已设置（可能在防抖方法中已设置）
			this.isUpdatingCart = true;
			this.disabledNumberInput = true;

			console.log('updateBuyNum: 开始更新数量', { id, buyNum, requestKey });

			// 创建新的请求Promise
			const requestPromise = this.$u.api
				.updateBuyNum(id, {
					buyNum: buyNum
				})
				.then(async res => {
					console.log('updateBuyNum: API请求成功', res);

					// 使用去重的购物车刷新方法
					await this.getCartByUserCenterIdWithDebounce();

					// 检查后端返回的赠品变化信息
					if (res.data && res.data.giftChanges) {
						if (res.data.giftChanges.removedGifts) {
							const removedGifts = res.data.giftChanges.removedGifts;
							const giftNames = removedGifts.map(gift => gift.goodsName || `SKU ${gift.skuId}`).join('、');
							this.$api.msg(`${giftNames} 因不满足条件已被移除`);
						}
						if (res.data.giftChanges.convertedGifts) {
							const convertedGifts = res.data.giftChanges.convertedGifts;
							const giftNames = convertedGifts.map(gift => gift.goodsName || `SKU ${gift.skuId}`).join('、');
							this.$api.msg(`${giftNames} 已自动转换为正常商品，将按原价计费`);
						}
					}

					return res;
				})
				.catch(async (error) => {
					console.error('updateBuyNum 请求失败:', error);
					// 失败时也要刷新购物车，确保数据一致性
					await this.getCartByUserCenterIdWithDebounce();
					throw error;
				})
				.finally(() => {
					console.log('updateBuyNum: 请求完成，清理状态', requestKey);

					// 清理请求缓存
					this.pendingUpdateRequests.delete(requestKey);
					this.disabledNumberInput = false;

					// 延迟重置更新状态，确保所有相关操作完成
					setTimeout(() => {
						this.isUpdatingCart = false;
						// 延迟调用满赠信息刷新，避免与watch冲突
						this.refreshFullGiveInfo();
					}, 100);
				});

			// 缓存请求Promise
			this.pendingUpdateRequests.set(requestKey, requestPromise);

			return requestPromise;
		},

		// 刷新满赠活动信息（带防抖）
		refreshFullGiveInfo() {
			// 清除之前的定时器
			if (this.refreshFullGiveTimer) {
				clearTimeout(this.refreshFullGiveTimer);
			}

			// 设置新的定时器，300ms后执行
			this.refreshFullGiveTimer = setTimeout(async () => {
				try {
					// 同时刷新满赠活动列表和当前满赠信息
					await Promise.all([
						this.getAllUsableFullGive(),
						this.getFullGiveInfo()
					]);
				} catch (error) {
					console.error('刷新满赠活动信息失败:', error);
				}
			}, 300);
		},

		//请求数据 获取商品列表
		async getCartByUserCenterId() {
			try {
				const { data } = await this.$u.api.getCartByUserCenterId();
				uni.stopPullDownRefresh();
				if (Array.isArray(data)) {
					this.goodsDataAll = {
						activityMoney: '0.00',
						cartNum: 0,
						checkNum: 0,
						expressMoney: 0,
						goodsData: [],
						goodsNum: 0,
						invalidData: [],
						payMoney: '0.00',
						preferential: '0.00',
						totalMoney: '0.00',
						vipDiscount: 0,
						vipDoubleDiscount: 0
					};
					this.goodsData = [];
					this.invalidData = [];
					this.$store.commit('commit_cartNum', 0);
					this.$store.commit('commit_cartPrice', 0);
					return;
				}
				this.goodsDataAll = data;
				this.invalidData = data.invalidData || [];
				this.hasAddFullGive = false;
				if (data.goodsData && data.goodsData.length) {
					data.goodsData.forEach(item => {
						item.checkedShop = item.shopGoodsData.every(itemG => itemG.selection === 5);
						item.shopGoodsData.forEach(goods => {
							goods.isShow = false;
							goods.limitQuantity = goods.inventoryNum;
							if (goods.sourceType == 2) {
								this.hasAddFullGive = true;
							}
						});
					});
					this.goodsData = data.goodsData;
					this.allChecked = this.goodsData.every(item => item.checkedShop);
				} else {
					this.goodsData = [];
				}
				this.$store.commit('commit_cartNum', data.goodsNum);
				this.$store.commit('commit_cartPrice', data.totalMoney);
			} catch (error) {
				console.error('获取购物车数据失败:', error);
			}
		},
		async getAllUsableFullBuy() {
			const { data } = await this.$u.api.getAllUsableFullBuy();
			if (Object.keys(data).length === 0) {
				this.fullBuy_list = [];
				return;
			}
			this.fullBuy_list = data;
		},
		async openFullBuyGoods(shopId) {
			this.fullBuyGoods_show = true;
			const shopTotalMoney = this.shopTotalMoneyMap[shopId];
			// 获取当前店铺的满额优惠信息
			const fullBuyInfo = this.fullBuy_list.find(item => item.shopId === shopId);
			if (!fullBuyInfo) return;

			// 获取当前满足条件的满额等级
			const currentLevels = fullBuyInfo.fullAmountLevels.filter(level => level.amount <= shopTotalMoney);

			// 提取所有满足条件的换购商品skuId
			const skuIds = currentLevels.reduce((acc, level) => {
				const levelSkuIds = level.goods.map(goods => goods.skuId);
				return [...acc, ...levelSkuIds];
			}, []);

			const params = {
				skuIds: skuIds,
				shopId: shopId,
				showSale: true,
				page: 1,
				pageSize: 20
			}
			const { data } = await this.$u.api.getGoodsByCategory(params);
			data.forEach(item => {
				this.shopFullBuyMap[shopId].fullAmountLevels.forEach(level => {
					const goods = level.goods.find(goods => goods.skuId === item.skuId);
					console.log(goods);
					if (goods) {
						item.exchangePrice = goods.exchangePrice;
						item.inventory = goods.inventory;
						item.limitQuantity = goods.limitQuantity;
						item.salePrice = goods.salePrice;
						item.skuId = goods.skuId;
						item.specGroup = goods.specGroup;
						item.buyNum = 0;
					}
				});
			});
			this.fullBuyGoods = data;
		},
		//  打开swipeAction
		openItem(i, gi) {
			// 先将正在被操作的swipeAction标记为打开状态，否则由于props的特性限制，
			// 原本为'false'，再次设置为'false'会无效
			let target = this.$u.deepClone(this.goodsData);
			target[i].shopGoodsData[gi].isShow = true;
			this.goodsData = target;
		},
		// 选中切换
		// type 1 单商品；2 店铺切换；3 全选切换
		async checkShop(checked, id, type, row, PRow) {
			const selection = checked ? 4 : 5;
			const params = {
				selection: selection,
				type: type
			};
			
			// 处理取消选中商品，移除关联的赠品
			if (type === 1 && selection === 4 && row && row.skuId) {
				// 检查是否有关联赠品
				if (this.selectedGiftMap[row.skuId]) {
					// 准备要删除的赠品cartId列表
					let giftCartIds = [];
					
					// 在购物车中找到属于该商品的赠品
					this.goodsData.forEach(shop => {
						shop.shopGoodsData.forEach(goods => {
							// 找出是赠品的商品(sourceType为2)
							if (goods.sourceType === 2) {
								// 检查是否是当前商品的赠品
								const fullGiveIds = Object.keys(this.selectedGiftMap[row.skuId] || {});
								for (let i = 0; i < fullGiveIds.length; i++) {
									const fullGiveId = fullGiveIds[i];
									const gifts = this.selectedGiftMap[row.skuId][fullGiveId] || {};
									if (gifts[goods.skuId]) {
										giftCartIds.push(goods.cartId);
									}
								}
							}
						});
					});
					
					// 如果找到了赠品，从购物车中删除
					if (giftCartIds.length > 0) {
						try {
							await this.$u.api.delCart({
								cartId: giftCartIds
							});
						} catch (error) {
							console.error('删除赠品失败:', error);
						}
					}
					
					// 清除该商品的赠品选择记录
					delete this.selectedGiftMap[row.skuId];
				}
			}
			
			// 处理全选/取消全选，清除所有赠品
			if (type === 3 && selection === 4) {
				// 准备要删除的赠品cartId列表
				let allGiftCartIds = [];
				
				// 在购物车中找到所有赠品
				this.goodsData.forEach(shop => {
					shop.shopGoodsData.forEach(goods => {
						if (goods.sourceType === 2) {
							allGiftCartIds.push(goods.cartId);
						}
					});
				});
				
				// 如果找到了赠品，从购物车中删除
				if (allGiftCartIds.length > 0) {
					try {
						await this.$u.api.delCart({
							cartId: allGiftCartIds
						});
					} catch (error) {
						console.error('删除所有赠品失败:', error);
					}
				}
				
				// 清除所有赠品选择记录
				this.selectedGiftMap = {};
			}
			
			// 店铺级别的选择/取消选择
			if (type === 2 && selection === 4) {
				let shopGiftCartIds = [];
				
				// 找出该店铺下所有商品的赠品
				if (row && row.shopGoodsData) {
					row.shopGoodsData.forEach(goods => {
						// 对于直接是赠品的商品
						if (goods.sourceType === 2) {
							shopGiftCartIds.push(goods.cartId);
						}
						
						// 对于有关联赠品的商品
						if (this.selectedGiftMap[goods.skuId]) {
							// 清除赠品记录
							delete this.selectedGiftMap[goods.skuId];
							
							// 在购物车中找出这些赠品
							this.goodsData.forEach(shop => {
								shop.shopGoodsData.forEach(item => {
									if (item.sourceType === 2) {
										shopGiftCartIds.push(item.cartId);
									}
								});
							});
						}
					});
				}
				
				// 如果找到了赠品，从购物车中删除
				if (shopGiftCartIds.length > 0) {
					try {
						await this.$u.api.delCart({
							cartId: shopGiftCartIds
						});
					} catch (error) {
						console.error('删除店铺赠品失败:', error);
					}
				}
			}
			
			if (type === 1) {
				params.cartId = id;
				// 前端手动把选择标示进行切换，优化操作体验
				row.selection = selection;
				PRow.checkedShop = PRow.shopGoodsData.every(itemG => itemG.selection === 5);
				this.allChecked = this.goodsData.every(item => item.checkedShop);
			} else if (type === 2) {
				params.shopId = id;
				// 前端手动把选择标示进行切换，优化操作体验
				row.checkedShop = !checked;
				row.shopGoodsData.forEach(item => {
					item.selection = selection;
				});
				this.allChecked = this.goodsData.every(item => item.checkedShop);
			} else {
				// 前端手动把选择标示进行切换，优化操作体验
				this.allChecked = !checked;
				this.goodsData.forEach(item => {
					item.checkedShop = !checked;
					item.shopGoodsData.forEach(goods => {
						goods.selection = selection;
					});
				});
			}
			this.$u.api
				.updateSelection({
					...params
				})
				.then(async res => {
					await this.getCartByUserCenterId();
					// 重新计算满赠活动信息
					this.refreshFullGiveInfo();
				});
		},
		// 移出商品
		delSelect() {
			let chooseGoods = [];
			this.goodsData.forEach(item => {
				item.shopGoodsData.forEach(itemC => {
					if (itemC.selection === 5) {
						chooseGoods.push(itemC.cartId);
					}
				});
			});
			this.deleteCartItem(chooseGoods);
		},
		// 清空失效
		clearInvalid() {
			let chooseGoods = this.invalidData.map(item => {
				return item.cartId;
			});
			this.deleteCartItem(chooseGoods);
		},
		// deleteCartItem 删除
		deleteCartItem(cartId, row, index, gIndex) {
			if (cartId.length === 0) {
				this.$api.msg('请选择要删除的商品');
				return;
			}
			uni.showModal({
				title: '提示',
				content: '你确定将此产品移除购物车',
				success: res => {
					if (res.confirm) {
						this.$u.api
							.delCart({
								cartId: cartId
							})
							.then(async res => {
								await this.getCartByUserCenterId();
								// 重新计算满赠活动信息
								this.refreshFullGiveInfo();
								// 检查赠品状态变化 - 已移至后端自动处理
								// await this.handleCartChangeGiftUpdate();
							});
					}
				}
			});
		},
		//创建订单
		createOrder() {
			// 验证账户状态
			this.userAudit();
			if (!this.isUserAudit) {
				return;
			}
			// }
			if (!this.goodsDataAll.totalMoney) {
				this.goPage('/pages/classification/cate', 'switchTab');
				return;
			}
			if (this.startDeliveryPrice && this.goodsDataAll.totalMoney < this.startDeliveryPrice) {
				// this.$api.msg('请选择要结算的商品');
				this.goPage('/pages/classification/cate', 'switchTab');
				return;
			}
			if (!this.goodsDataAll.checkNum) {
				this.$api.msg('请选择要结算的商品');
				return;
			}
			let isSub = true;
			for (let i in this.goodsData) {
				let item = this.goodsData[i];
				for (let g in item.shopGoodsData) {
					let goods = item.shopGoodsData[g];
					if (goods.selection === 5 && goods.isInvalid === 4) {
						isSub = false;
						break;
					}
				}
			}
			if (!isSub) {
				this.$api.msg('抱歉，您要结算的商品中包含失效商品，请取消后再次进行提交');
				return;
			}
			uni.navigateTo({
				url: `/pagesT/order/createOrder`
			});
		},
		async getAllUsableFullGive() {
			try {
				// 构建购物车商品列表
				const cartSkus = [];
				this.goodsDataAll.goodsData.forEach(shop => {
					shop.shopGoodsData.forEach(goods => {
						if (goods.selection === 5) {
							cartSkus.push({
								skuId: goods.skuId,
								price: goods.price,
								quantity: goods.buyNum,
								amount: this.$NP.times(Number(goods.price), Number(goods.buyNum))
							});
						}
					});
				});

				if (cartSkus.length == 0) {
					return;
				}
				
				// 使用getAvailableGifts接口替代原来的getAllUseable接口
				const orderAmount = this.goodsDataAll.totalMoney;
				const shopId = this.goodsDataAll.goodsData.length > 0 ? this.goodsDataAll.goodsData[0].shopId : '';

				// 获取当前购物车中商品的仓库ID（取第一个商品的仓库ID作为订单仓库）
				let warehouseId = null;
				if (this.goodsDataAll.goodsData.length > 0 && this.goodsDataAll.goodsData[0].shopGoodsData.length > 0) {
					const firstGoods = this.goodsDataAll.goodsData[0].shopGoodsData.find(goods => goods.selection === 5);
					if (firstGoods && firstGoods.warehouseId) {
						warehouseId = firstGoods.warehouseId;
					}
				}

				const { data } = await this.$u.api.getAvailableGifts({
					orderAmount,
					shopId,
					warehouseId,
					orderSkus: cartSkus
				});
				
				if (!data || !Array.isArray(data)) {
					this.fullGive_list = [];
					this.itemFullGives = {};
					return;
				}
				
				// 处理每个满赠活动的条件和满足情况
				const processedData = data.map(fullGive => {
					// 从活动中获取满赠类型和条件
					const availableLevel = fullGive.availableLevel;
					const nextLevel = fullGive.nextLevel;

					// 如果有可用的满赠级别
					if (availableLevel) {
						const giftType = availableLevel.giftType || 1;

						if (giftType === 1) { // 金额满赠
							return {
								...fullGive,
								id: fullGive.id,
								title: fullGive.title,
								shopId: fullGive.shopId,
								giftType: 1,
								targetType: availableLevel.targetType || 1,
								requiredAmount: availableLevel.requiredAmount,
								skus: availableLevel.skus || [],
								balance: 0 // 已满足条件
							};
						} else if (giftType === 2) { // 数量满赠
							// 重新计算倍数，确保使用最新的购物车数据
							let calculatedMultiple = 1;
							if (fullGive.isMultipleGift == 5) { // 5表示启用倍数赠送
								// 计算实际购买的目标商品数量
								let totalQuantity = 0;
								let requiredQuantity = availableLevel.requiredQuantity || 1; // 默认使用级别的requiredQuantity

								// 如果有指定商品，只计算指定商品的数量
								if (availableLevel.targetSkus && Array.isArray(availableLevel.targetSkus) && availableLevel.targetSkus.length > 0) {
									const targetSkuIds = availableLevel.targetSkus.map(target => {
										if (typeof target === 'object' && target.skuId) {
											return target.skuId;
										}
										return target;
									});

									// 如果只有一个指定商品且有requiredQuantity，使用该值
									if (availableLevel.targetSkus.length === 1) {
										const targetInfo = availableLevel.targetSkus[0];
										if (typeof targetInfo === 'object' && targetInfo.requiredQuantity) {
											requiredQuantity = targetInfo.requiredQuantity;
										}
									}

									// 遍历购物车，计算指定商品的总数量
									this.goodsDataAll.goodsData.forEach(shop => {
										shop.shopGoodsData.forEach(goods => {
											if (goods.selection === 5 && targetSkuIds.includes(goods.skuId)) {
												totalQuantity += goods.buyNum;
											}
										});
									});
								} else {
									// 计算所有商品的数量
									this.goodsDataAll.goodsData.forEach(shop => {
										shop.shopGoodsData.forEach(goods => {
											if (goods.selection === 5) {
												totalQuantity += goods.buyNum;
											}
										});
									});
								}

								// 计算倍数：实际购买数量 ÷ 满足数量
								calculatedMultiple = Math.floor(totalQuantity / requiredQuantity);
							}

							return {
								...fullGive,
								id: fullGive.id,
								title: fullGive.title,
								shopId: fullGive.shopId,
								giftType: 2,
								requiredQuantity: availableLevel.requiredQuantity,
								targetSkus: availableLevel.targetSkus,
								skus: availableLevel.skus || [],
								balance: 0, // 已满足条件
								giftMultiple: Math.max(1, calculatedMultiple), // 重新计算的倍数
								isMultipleGift: fullGive.isMultipleGift || 4 // 保持原始值，4表示未启用，5表示启用
							};
						}
					}
					// 如果有下一个满赠级别（未满足条件）
					else if (nextLevel) {
						const giftType = nextLevel.giftType || 1;
						
						if (giftType === 1) { // 金额满赠
							return {
								...fullGive,
								id: fullGive.id,
								title: fullGive.title,
								shopId: fullGive.shopId,
								giftType: 1,
								targetType: nextLevel.targetType || 1,
								requiredAmount: nextLevel.requiredAmount,
								skus: nextLevel.skus || [],
								balance: fullGive.gapAmount || Math.max(nextLevel.requiredAmount - orderAmount, 0)
							};
						} else if (giftType === 2) { // 数量满赠
							return {
								...fullGive,
								id: fullGive.id,
								title: fullGive.title,
								shopId: fullGive.shopId,
								giftType: 2,
								requiredQuantity: nextLevel.requiredQuantity,
								targetSkus: nextLevel.targetSkus,
								skus: nextLevel.skus || [],
								balance: fullGive.gapQuantity || nextLevel.requiredQuantity
							};
						}
					}
					
					return fullGive;
				});
				
				// 过滤掉没有赠品的满赠活动
				const validFullGives = processedData.filter(item => {
					return item && item.skus && item.skus.length > 0;
				});
				
				// 分类处理满赠活动
				const itemFullGives = {};
				const globalFullGives = [];
				
				validFullGives.forEach(item => {
					// 金额满赠且targetType为1(订单总金额)的显示在底部
					if ((item.giftType === 1 && (!item.targetType || item.targetType === 1))) {
						globalFullGives.push(item);
					} 
					// 指定商品满赠或指定商品金额满赠，映射到对应商品
					else if ((item.giftType === 1 && item.targetType === 2) || (item.giftType === 2 && item.targetSkus && item.targetSkus.length)) {
						// 遍历目标商品，将满赠活动添加到对应商品映射
						(item.targetSkus || []).forEach(targetSku => {
							const skuId = typeof targetSku === 'object' ? targetSku.skuId : targetSku;
							if (!itemFullGives[skuId]) itemFullGives[skuId] = [];
							itemFullGives[skuId].push(item);
						});
					}
				});
				
				// 更新数据
				this.itemFullGives = itemFullGives;
				this.fullGive_list = globalFullGives;
			} catch (error) {
				console.error('获取满赠活动失败:', error);
				this.fullGive_list = [];
				this.itemFullGives = {};
			}
		},
		handleFullGiveSel(fullGive) {
			this.fullGiveItem = fullGive;
			this.fullGiveGoods_show = true;
			// 设置当前选中的满赠活动信息
			this.fullGiveInfo = {
				id: fullGive.id,
				title: fullGive.title,
				shopId: fullGive.shopId,
				isMultipleGift: fullGive.isMultipleGift || 4, // 保持原始值，4表示未启用，5表示启用
				availableLevel: {
					requiredAmount: fullGive.requiredAmount,
					requiredQuantity: fullGive.requiredQuantity,
					skus: fullGive.skus,
					giftType: fullGive.giftType,
					targetType: fullGive.targetType || 1,
					targetSkus: fullGive.targetSkus,
					giftMultiple: fullGive.giftMultiple || 1
				}
			};
			// 加载满赠赠品列表，确保数据格式正确
			if (!fullGive.skus || !Array.isArray(fullGive.skus)) {
				console.warn('满赠活动缺少SKU数据:', fullGive);
				this.fullGiveGiftsList = [];
				return;
			}

			this.fullGiveGiftsList = fullGive.skus.map(sku => {
				// 确保图片数组格式正确
				let images = [];
				if (sku.images && Array.isArray(sku.images)) {
					images = sku.images;
				} else if (sku.image) {
					images = [sku.image];
				} else if (sku.goodsImage) {
					images = [sku.goodsImage];
				}

				// 确保规格数据格式正确
				let specGroup = [];
				if (sku.specGroup && Array.isArray(sku.specGroup)) {
					specGroup = sku.specGroup;
				}

				return {
					...sku,
					title: sku.goodsName || sku.title || '商品名称',
					goodsName: sku.goodsName || sku.title || '商品名称',
					stock: sku.inventory || sku.stock || 0,
					inventory: sku.inventory || sku.stock || 0,
					unitName: sku.unitName || '件',
					images: images,
					image: images.length > 0 ? images[0] : '',
					goodsImage: images.length > 0 ? images[0] : '',
					specGroup: specGroup,
					price: sku.price || sku.salePrice || 0,
					salePrice: sku.salePrice || sku.price || 0
				};
			});
		},
		async handleFullGiveGoodsSel(goods) {
			if (!this.fullGiveInfo || !goods) return;

			try {
				// 计算赠品数量（考虑倍数赠送）
				let giftQuantity = 1; // 默认数量为1
				if (this.fullGiveInfo.isMultipleGift == 5 && this.fullGiveInfo.availableLevel && this.fullGiveInfo.availableLevel.giftMultiple > 1) {
					giftQuantity = this.fullGiveInfo.availableLevel.giftMultiple;
				}



				// 检查库存限制
				const availableStock = goods.stock || goods.inventory || 0;
				if (giftQuantity > availableStock) {
					this.$api.msg(`赠品库存不足，最多可选${availableStock}件`);
					return;
				}

				// 检查赠品上限限制
				const remainingCount = this.getRemainingGiftCount(this.fullGiveInfo);
				if (giftQuantity > remainingCount) {
					this.$api.msg(`超出赠品上限，剩余可选${remainingCount}件`);
					return;
				}

				// 构建要添加到购物车的赠品数据
				const goodsData = [{
					goodsBasicId: goods.basicGoodsId || goods.goodsBasicId,
					goodsId: goods.goodsId || goods.id,
					goodsCode: goods.goodsCode || goods.code || '',
					buyNum: giftQuantity, // 根据倍数设置赠品数量
					shopId: this.fullGiveInfo.shopId,
					source: this.$common.source(),
					skuId: goods.skuId,
					sourceType: 2, // 2表示赠品
					fullGiveId: this.fullGiveInfo.id, // 添加满赠活动ID
				}];

				// 直接调用addCart接口将赠品添加到购物车
				const { data } = await this.$u.api.addCart({ goodsData });

				if (data) {
					if (giftQuantity > 1) {
						this.$api.msg(`选择赠品成功，获得${giftQuantity}件赠品`);
					} else {
						this.$api.msg('选择赠品成功');
					}
					this.fullGiveGoods_show = false;
					await this.getCartByUserCenterId(); // 刷新购物车
					// 重新计算满赠活动信息
					this.refreshFullGiveInfo();
				}
			} catch (error) {
				console.error('选择满赠赠品失败:', error);
				this.$api.msg(error.message || '选择满赠赠品失败，请稍后重试');
			}
		},
		cancelTip() {
			this.loginTip = false;
		},
		addCard(goods) {
			this.goodsData.push(goods);
		},
		handleFullBuyGoodsAdd(goods) {
			goods.buyNum = 1;
		},
		async handleFullBuyGoodsConfirm() {
			const goodsData = [];
			for (let i = 0; i < this.fullBuyGoods.length; i++) {
				const goods = this.fullBuyGoods[i];
				if (goods.buyNum > 0) {
					goodsData.push(
						{
							goodsBasicId: goods.basicGoodsId,
							goodsId: goods.id,
							buyNum: goods.buyNum,
							shopId: goods.shopId,
							source: this.$common.source(),
							skuId: goods.skuId,
							sourceType: 3,
						}
					);
				}
			}
			const {data} = await this.$u.api.addCart({goodsData});
			await this.getCartByUserCenterId();
			// 重新计算满赠活动信息
			this.refreshFullGiveInfo();
			this.fullBuyGoods_show = false;
		},
		// 获取满赠活动信息
		async getFullGiveInfo() {
			try {
				this.fullGiveLoading = true;
				
				// 构建订单商品列表
				const orderSkus = [];
				this.goodsDataAll.goodsData.forEach(shop => {
					shop.shopGoodsData.forEach(goods => {
						if (goods.selection === 5) {
							orderSkus.push({
								skuId: goods.skuId,
								price: goods.price,
								quantity: goods.buyNum,
								amount: this.$NP.times(Number(goods.price), Number(goods.buyNum)) // 添加 amount 字段保证后端兼容性
							});
						}
					});
				});

				console.log(orderSkus)
				if (orderSkus.length == 0) {
					return;
				}
				
				const orderAmount = this.goodsDataAll.totalMoney;
				const shopId = this.goodsDataAll.goodsData.length > 0 ? this.goodsDataAll.goodsData[0].shopId : '';

				// 获取当前购物车中商品的仓库ID（取第一个商品的仓库ID作为订单仓库）
				let warehouseId = null;
				if (this.goodsDataAll.goodsData.length > 0 && this.goodsDataAll.goodsData[0].shopGoodsData.length > 0) {
					const firstGoods = this.goodsDataAll.goodsData[0].shopGoodsData.find(goods => goods.selection === 5);
					if (firstGoods && firstGoods.warehouseId) {
						warehouseId = firstGoods.warehouseId;
					}
				}

				const { data } = await this.$u.api.getAvailableGifts({
					orderAmount,
					shopId,
					warehouseId,
					orderSkus
				});
				
				if (data && data.length > 0) {
					this.fullGiveInfo = data[0];
					
					// 如果有可用的满赠活动且满足条件，预加载赠品列表
					if (this.fullGiveInfo.availableLevel && this.fullGiveInfo.gapAmount <= 0) {
						await this.loadFullGiveGifts();
					}
				} else {
					this.fullGiveInfo = null;
				}
			} catch (error) {
				console.error('获取满赠活动信息失败:', error);
				this.$api.msg('获取满赠活动信息失败，请稍后重试');
				this.fullGiveInfo = null;
			} finally {
				this.fullGiveLoading = false;
			}
		},
		
		// 加载满赠赠品列表
		async loadFullGiveGifts() {
			if (!this.fullGiveInfo || !this.fullGiveInfo.availableLevel) return;
			
			try {
				this.fullGiveGiftsLoading = true;
				const level = this.fullGiveInfo.availableLevel;
				
				// 直接使用满赠活动中的赠品信息
				if (level.skus && level.skus.length > 0) {
					this.fullGiveGiftsList = level.skus.map(sku => ({
						...sku,
						title: sku.goodsName || sku.title,
						stock: sku.inventory || sku.stock,
						unitName: sku.unitName || '件'
					}));
				} else {
					this.fullGiveGiftsList = [];
				}
			} catch (error) {
				console.error('加载满赠赠品列表失败:', error);
				this.$api.msg('加载满赠赠品列表失败，请稍后重试');
				this.fullGiveGiftsList = [];
			} finally {
				this.fullGiveGiftsLoading = false;
			}
		},
		// 检查商品是否满足满赠条件
		hasItemFullGiveCondition(goods) {
			// 如果商品未选中，直接返回false
			if (!goods || !this.itemFullGives[goods.skuId] || goods.selection !== 5) return false;

			// 获取商品对应的满赠活动
			const fullGives = this.itemFullGives[goods.skuId];

			// 检查是否至少有一个满赠活动满足条件
			return fullGives.some(item => {
				return this.checkSingleFullGiveCondition(item, goods);
			});
		},

		// 检查单个满赠活动条件
		checkSingleFullGiveCondition(fullGive, goods) {
			if (!fullGive || !goods) return false;

			if (fullGive.giftType === 1) {
				// 金额满赠
				if (fullGive.targetType === 1) {
					// 订单总金额满赠 - 需要检查整个购物车的总金额
					return this.goodsDataAll.totalMoney >= fullGive.requiredAmount;
				} else if (fullGive.targetType === 2) {
					// 指定商品金额满赠 - 需要检查指定商品的总金额
					const targetSkus = fullGive.targetSkuIds || fullGive.targetSkus;
					return this.calculateTargetSkusAmount(targetSkus, goods.shopId) >= fullGive.requiredAmount;
				}
			} else if (fullGive.giftType === 2) {
				// 数量满赠 - 需要检查每个SKU的独立数量要求
				const targetSkus = fullGive.targetSkuIds || fullGive.targetSkus;
				const validationResult = this.validateIndividualSkuQuantities(targetSkus, goods.shopId);
				return validationResult.satisfied;
			}

			return false;
		},

		// 计算指定SKU的总金额（在指定店铺内）
		calculateTargetSkusAmount(targetSkus, shopId) {
			if (!targetSkus || !Array.isArray(targetSkus) || !this.goodsDataAll.goodsData) return 0;

			let totalAmount = 0;

			// 找到对应的店铺
			const shop = this.goodsDataAll.goodsData.find(s => s.shopId === shopId);
			if (!shop || !shop.shopGoodsData) return 0;

			// 提取SKU ID列表
			const targetSkuIds = targetSkus.map(target => {
				return typeof target === 'object' ? target.skuId : target;
			});

			// 计算指定SKU的总金额
			shop.shopGoodsData.forEach(goods => {
				if (goods.selection === 5 && targetSkuIds.includes(goods.skuId)) {
					totalAmount += this.$NP.times(Number(goods.price), Number(goods.buyNum));
				}
			});

			return totalAmount;
		},

		// 验证每个SKU的独立数量要求（在指定店铺内）
		validateIndividualSkuQuantities(targetSkus, shopId) {
			if (!targetSkus || !Array.isArray(targetSkus) || !this.goodsDataAll.goodsData) {
				return { satisfied: false, message: '配置错误', skuDetails: [] };
			}

			// 找到对应的店铺
			const shop = this.goodsDataAll.goodsData.find(s => s.shopId === shopId);
			if (!shop || !shop.shopGoodsData) {
				return { satisfied: false, message: '店铺数据错误', skuDetails: [] };
			}

			// 构建购物车商品映射
			const cartSkuMap = {};
			shop.shopGoodsData.forEach(goods => {
				if (goods.selection === 5) {
					cartSkuMap[goods.skuId] = Number(goods.buyNum);
				}
			});

			const skuDetails = [];
			const unsatisfiedSkus = [];
			let allSatisfied = true;

			// 逐个检查每个目标SKU
			targetSkus.forEach(target => {
				const skuId = typeof target === 'object' ? target.skuId : target;
				const requiredQuantity = typeof target === 'object' ? (target.requiredQuantity || 1) : 1;
				const currentQuantity = cartSkuMap[skuId] || 0;
				const satisfied = currentQuantity >= requiredQuantity;

				const skuDetail = {
					skuId,
					requiredQuantity,
					currentQuantity,
					satisfied
				};

				skuDetails.push(skuDetail);

				if (!satisfied) {
					allSatisfied = false;
					const shortfall = requiredQuantity - currentQuantity;
					unsatisfiedSkus.push({
						skuId,
						shortfall,
						requiredQuantity,
						currentQuantity
					});
				}
			});

			// 构造错误信息
			let message = '';
			if (!allSatisfied) {
				const messages = unsatisfiedSkus.map(unsatisfied => {
					const goodsName = this.getGoodsNameBySkuId(unsatisfied.skuId, shopId);
					return `${goodsName || `SKU ${unsatisfied.skuId}`} 还需购买${unsatisfied.shortfall}件`;
				});
				message = messages.join('，') + '才可获得赠品';
			}

			return {
				satisfied: allSatisfied,
				message,
				skuDetails,
				unsatisfiedSkus
			};
		},

		// 计算指定SKU的总数量（在指定店铺内）- 保留兼容性
		calculateTargetSkusQuantity(targetSkus, shopId) {
			const result = this.validateIndividualSkuQuantities(targetSkus, shopId);
			// 返回总数量（用于向后兼容）
			return result.skuDetails.reduce((total, detail) => total + detail.currentQuantity, 0);
		},

		// 根据SKU ID获取商品名称
		getGoodsNameBySkuId(skuId, shopId) {
			if (!this.goodsDataAll.goodsData) return null;

			const shop = this.goodsDataAll.goodsData.find(s => s.shopId === shopId);
			if (!shop || !shop.shopGoodsData) return null;

			const goods = shop.shopGoodsData.find(g => g.skuId === skuId);
			return goods ? goods.goodsName : null;
		},

		// 获取满赠条件详细信息
		getFullGiveConditionDetail(goods) {
			if (!goods || !this.itemFullGives[goods.skuId] || goods.selection !== 5) {
				return { satisfied: false, message: '商品未选中' };
			}

			const fullGives = this.itemFullGives[goods.skuId];

			for (let item of fullGives) {
				if (item.giftType === 1) {
					// 金额满赠
					if (item.targetType === 1) {
						// 订单总金额满赠
						const currentAmount = this.goodsDataAll.totalMoney;
						if (currentAmount >= item.requiredAmount) {
							return { satisfied: true, message: '' };
						} else {
							const shortfall = this.$NP.minus(item.requiredAmount, currentAmount);
							return {
								satisfied: false,
								message: `还需购买${shortfall}元商品才可获得赠品`
							};
						}
					} else if (item.targetType === 2) {
						// 指定商品金额满赠
						const targetSkus = item.targetSkuIds || item.targetSkus;
						const currentAmount = this.calculateTargetSkusAmount(targetSkus, goods.shopId);
						if (currentAmount >= item.requiredAmount) {
							return { satisfied: true, message: '' };
						} else {
							const shortfall = this.$NP.minus(item.requiredAmount, currentAmount);
							return {
								satisfied: false,
								message: `还需购买${shortfall}元指定商品才可获得赠品`
							};
						}
					}
				} else if (item.giftType === 2) {
					// 数量满赠 - 每个SKU独立校验
					const targetSkus = item.targetSkuIds || item.targetSkus;
					const validationResult = this.validateIndividualSkuQuantities(targetSkus, goods.shopId);

					if (validationResult.satisfied) {
						return { satisfied: true, message: '' };
					} else {
						return {
							satisfied: false,
							message: validationResult.message
						};
					}
				}
			}

			return { satisfied: false, message: '不满足满赠条件' };
		},

		// 处理禁用赠品的点击事件
		handleDisabledGiftClick() {
			if (this.currentItemFullGive && this.currentItemFullGive.currentGoods) {
				const conditionDetail = this.getFullGiveConditionDetail(this.currentItemFullGive.currentGoods);
				this.$api.msg(conditionDetail.message || '不满足满赠条件');
			} else {
				this.$api.msg('不满足满赠条件');
			}
		},

		// 验证购物车中所有满赠活动条件 - 已移至后端自动处理
		// async validateAllFullGiveConditions(action = 'check') {
		// 	try {
		// 		const result = await this.$u.api.validateCartFullGiveConditions({ action });
		// 		return result;
		// 	} catch (error) {
		// 		console.error('验证满赠条件失败:', error);
		// 		return { data: { invalidGifts: [], validGifts: [] } };
		// 	}
		// },



		// 处理购物车数量变化后的赠品状态更新 - 已移至后端自动处理
		// async handleCartChangeGiftUpdate() {
		// 	try {
		// 		const validationResult = await this.validateAllFullGiveConditions('auto');
		// 		const invalidGifts = validationResult.data?.invalidGifts || [];
		// 		const processResult = validationResult.data?.processResult;

		// 		if (invalidGifts.length > 0 && processResult) {
		// 			// 显示自动转换结果提示
		// 			this.showAutoConvertResult(processResult);
		// 		}
		// 	} catch (error) {
		// 		console.error('检查赠品状态失败:', error);
		// 	}
		// },

		// 显示自动转换结果提示 - 已移至后端自动处理
		// showAutoConvertResult(processResult) {
		// 	const { processedItems, totalProcessed } = processResult;

		// 	if (totalProcessed > 0) {
		// 		const convertedNames = processedItems
		// 			.filter(item => item.action === 'converted')
		// 			.map(item => item.goodsName || `SKU ${item.skuId}`)
		// 			.join('、');

		// 		if (convertedNames) {
		// 			this.$api.msg(`${convertedNames} 已自动转换为正常商品，将按原价计费`);
		// 		}
		// 	}
		// },

		// 批量处理无效赠品 - 已移至后端自动处理
		// async handleInvalidGifts(invalidGifts, action) {
		// 	try {
		// 		const result = await this.$u.api.handleInvalidGifts({
		// 			invalidGifts,
		// 			action
		// 		});

		// 		if (result.data) {
		// 			const { processedItems, errors, totalProcessed } = result.data;

		// 			if (action === 'remove') {
		// 				this.$api.msg(`已移除${totalProcessed}个赠品`);
		// 			} else if (action === 'convert') {
		// 				this.$api.msg(`已转换${totalProcessed}个赠品为正常商品`);
		// 			}

		// 			if (errors.length > 0) {
		// 				console.error('处理赠品时出现错误:', errors);
		// 			}

		// 			// 刷新购物车
		// 			await this.getCartByUserCenterId();
		// 			this.refreshFullGiveInfo();
		// 		}
		// 	} catch (error) {
		// 		console.error('处理赠品失败:', error);
		// 		this.$api.msg('操作失败，请稍后重试');
		// 	}
		// },

		// 显示商品满赠弹窗
		showItemFullGive(goods) {
			if (!goods || !this.itemFullGives[goods.skuId]) return;

			// 获取满足条件的满赠活动
			const fullGives = this.itemFullGives[goods.skuId];
			if (fullGives.length === 0) return;

			// 选择第一个满赠活动显示
			const fullGive = fullGives[0];
			fullGive.currentGoods = goods; // 保存当前商品信息

			// 计算倍数赠送倍数（仅对数量满赠有效）
			if (fullGive.giftType === 2 && fullGive.isMultipleGift == 5) { // 5表示启用倍数赠送
				let requiredQuantity = fullGive.requiredQuantity; // 默认使用活动的requiredQuantity
				let actualQuantity = goods.buyNum;

				// 如果有targetSkus，需要计算所有相关商品的数量
				if (fullGive.targetSkus && Array.isArray(fullGive.targetSkus) && fullGive.targetSkus.length > 0) {
					const targetSkuIds = fullGive.targetSkus.map(target => {
						if (typeof target === 'object' && target.skuId) {
							return target.skuId;
						}
						return target;
					});

					// 如果只有一个指定商品且有requiredQuantity，使用该值
					if (fullGive.targetSkus.length === 1) {
						const targetInfo = fullGive.targetSkus[0];
						if (typeof targetInfo === 'object' && targetInfo.requiredQuantity) {
							requiredQuantity = targetInfo.requiredQuantity;
						}
					}

					// 如果当前商品不在目标商品列表中，倍数为1
					if (!targetSkuIds.includes(goods.skuId)) {
						fullGive.giftMultiple = 1;
					} else {
						// 计算所有目标商品的总数量
						actualQuantity = 0;
						this.goodsDataAll.goodsData.forEach(shop => {
							shop.shopGoodsData.forEach(cartGoods => {
								if (cartGoods.selection === 5 && targetSkuIds.includes(cartGoods.skuId)) {
									actualQuantity += cartGoods.buyNum;
								}
							});
						});

						// 计算倍数
						const multiple = Math.floor(actualQuantity / requiredQuantity);
						fullGive.giftMultiple = Math.max(1, multiple);
					}
				} else {
					// 没有指定商品，直接用当前商品数量计算
					const multiple = Math.floor(actualQuantity / requiredQuantity);
					fullGive.giftMultiple = Math.max(1, multiple);
				}
			} else {
				fullGive.giftMultiple = 1;
			}

			this.currentItemFullGive = fullGive;
			this.currentItemFullGiveGifts = fullGive.skus.map(sku => ({
				...sku,
				title: sku.goodsName || sku.title,
				stock: sku.inventory || sku.stock,
				unitName: sku.unitName || '件'
			}));

			this.itemFullGiveShow = true;
		},

		// 选择商品满赠赠品
		async handleItemFullGiveGoodsSel(goods, fullGive = null, currentGoods = null) {
			const useFullGive = fullGive || this.currentItemFullGive;
			const useGoods = currentGoods || useFullGive.currentGoods;

			if (!useFullGive || !useGoods) return;

			// 第一层检查：前端条件验证
			if (!this.hasItemFullGiveCondition(useGoods)) {
				const conditionDetail = this.getFullGiveConditionDetail(useGoods);
				this.$api.msg(conditionDetail.message || '商品数量不满足满赠条件');
				return;
			}

			// 第二层检查：后端条件验证（防止绕过前端限制）
			try {
				const validateResult = await this.$u.api.validateFullGiveCondition({
					fullGiveId: useFullGive.id
				});

				if (!validateResult.data.satisfied) {
					this.$api.msg(validateResult.data.message || '不满足满赠条件');
					return;
				}
			} catch (error) {
				console.error('验证满赠条件失败:', error);
				this.$api.msg('验证满赠条件失败，请稍后重试');
				return;
			}
			
			// 检查是否已达到赠品上限
			if (this.isGiftLimitReached(useGoods, useFullGive)) {
				const remainingCount = this.getRemainingGiftCount(useFullGive);
				this.$api.msg(`已达到赠品上限，剩余可选：${remainingCount}件`);
				this.itemFullGiveShow = false;
				return;
			}
			
			// 检查是否已添加该赠品
			if (this.selectedGiftMap[useGoods.skuId] && this.selectedGiftMap[useGoods.skuId][goods.skuId]) {
				this.$api.msg('已添加该赠品');
				this.itemFullGiveShow = false;
				return;
			}
			
			try {
				// 计算赠品数量（考虑倍数赠送）
				let giftQuantity = 1; // 默认数量为1
				if (useFullGive.isMultipleGift == 5 && useFullGive.giftMultiple > 1) {
					giftQuantity = useFullGive.giftMultiple;
				}



				// 检查库存限制
				const availableStock = goods.stock || goods.inventory || 0;
				if (giftQuantity > availableStock) {
					this.$api.msg(`赠品库存不足，最多可选${availableStock}件`);
					return;
				}

				// 检查赠品上限限制
				const remainingCount = this.getRemainingGiftCount(useFullGive);
				if (giftQuantity > remainingCount) {
					this.$api.msg(`超出赠品上限，剩余可选${remainingCount}件`);
					return;
				}

				// 构建要添加到购物车的赠品数据
				const goodsData = [{
					goodsBasicId: goods.basicGoodsId || goods.goodsBasicId,
					goodsId: goods.goodsId || goods.id,
					goodsCode: goods.goodsCode || goods.code || '',
					buyNum: giftQuantity, // 根据倍数设置赠品数量
					shopId: useFullGive.shopId,
					source: this.$common.source(),
					skuId: goods.skuId,
					sourceType: 2, // 2表示赠品
					fullGiveId: useFullGive.id, // 添加满赠活动ID
				}];

				// 直接调用addCart接口将赠品添加到购物车
				const { data } = await this.$u.api.addCart({ goodsData });

				if (data) {
					// 更新赠品选择状态
					if (!this.selectedGiftMap[useGoods.skuId]) {
						this.selectedGiftMap[useGoods.skuId] = {};
					}
					if (!this.selectedGiftMap[useGoods.skuId][useFullGive.id]) {
						this.selectedGiftMap[useGoods.skuId][useFullGive.id] = {};
					}
					this.selectedGiftMap[useGoods.skuId][useFullGive.id][goods.skuId] = true;

					if (giftQuantity > 1) {
						this.$api.msg(`选择赠品成功，获得${giftQuantity}件赠品`);
					} else {
						this.$api.msg('选择赠品成功');
					}
					this.itemFullGiveShow = false;
					await this.getCartByUserCenterId(); // 刷新购物车
					// 重新计算满赠活动信息
					this.refreshFullGiveInfo();
				}
			} catch (error) {
				console.error('选择满赠赠品失败:', error);
				this.$api.msg(error.message || '选择满赠赠品失败，请稍后重试');
			}
		},

		// 增加商品数量以满足满赠条件
		addMoreForFullGive() {
			if (!this.currentItemFullGive || !this.currentItemFullGive.currentGoods) return;
			
			const goods = this.currentItemFullGive.currentGoods;
			const fullGive = this.currentItemFullGive;
			
			let targetNum = goods.buyNum;
			
			if (fullGive.giftType === 1 && fullGive.targetType === 2) {
				// 金额满赠，计算需要的数量
				const currentAmount = this.$NP.times(Number(goods.price), Number(goods.buyNum));
				const needAmount = fullGive.requiredAmount;
				if (currentAmount < needAmount) {
					// 计算需要增加的数量
					const neededAmount = this.$NP.minus(needAmount, currentAmount);
					const additionalUnits = Math.ceil(this.$NP.divide(neededAmount, Number(goods.price)));
					targetNum = this.$NP.plus(goods.buyNum, additionalUnits);
				}
			} else if (fullGive.giftType === 2) {
				// 数量满赠，计算需要的数量
				let requiredQuantity = fullGive.requiredQuantity; // 默认使用活动的整体数量要求
				
				// 如果有targetSkus，检查是否有针对这个商品的特定要求
				if (fullGive.targetSkus && Array.isArray(fullGive.targetSkus)) {
					const targetSku = fullGive.targetSkus.find(target => {
						// 处理targetSku可能是对象或者简单ID的情况
						if (typeof target === 'object' && target.skuId) {
							return target.skuId === goods.skuId;
						}
						return target === goods.skuId;
					});
					
					// 如果找到了特定要求，并且有requiredQuantity字段
					if (targetSku && typeof targetSku === 'object' && targetSku.requiredQuantity) {
						requiredQuantity = targetSku.requiredQuantity;
					}
				}
				
				targetNum = requiredQuantity;
			}
			
			// 更新购物车数量
			this.updateBuyNum(goods.cartId, targetNum);
			this.itemFullGiveShow = false;
		},
		// 获取满赠差额文本
		getItemFullGiveGapText(goods) {
			if (!goods || !this.itemFullGives[goods.skuId]) return '';
			
			const fullGives = this.itemFullGives[goods.skuId];
			if (fullGives.length === 0) return '';
			
			const fullGive = fullGives[0]; // 取第一个满赠活动
			
			// 金额满赠
			if (fullGive.giftType === 1 && fullGive.targetType === 2) {
				const goodsAmount = this.$NP.times(Number(goods.price), Number(goods.buyNum));
				const gap = this.$NP.minus(fullGive.requiredAmount, goodsAmount);
				if (gap > 0) {
					return `再买¥${gap.toFixed(2)}`;
				}
			} 
			// 数量满赠
			else if (fullGive.giftType === 2) {
				// 获取当前商品的目标数量要求
				let requiredQuantity = fullGive.requiredQuantity; // 默认使用活动的整体数量要求
				
				// 如果有targetSkus，检查是否有针对这个商品的特定要求
				if (fullGive.targetSkus && Array.isArray(fullGive.targetSkus)) {
					const targetSku = fullGive.targetSkus.find(target => {
						// 处理targetSku可能是对象或者简单ID的情况
						if (typeof target === 'object' && target.skuId) {
							return target.skuId === goods.skuId;
						}
						return target === goods.skuId;
					});
					
					// 如果找到了特定要求，并且有requiredQuantity字段
					if (targetSku && typeof targetSku === 'object' && targetSku.requiredQuantity) {
						requiredQuantity = targetSku.requiredQuantity;
					}
				}
				
				const gap = requiredQuantity - goods.buyNum;
				if (gap > 0) {
					return `再买${gap}件`;
				}
			}
			
			return '';
		},
	},

	onUnload() {
		// 清理定时器
		if (this.refreshFullGiveTimer) {
			clearTimeout(this.refreshFullGiveTimer);
			this.refreshFullGiveTimer = null;
		}

		// 清理数量更新防抖定时器
		if (this.updateBuyNumTimer) {
			clearTimeout(this.updateBuyNumTimer);
			this.updateBuyNumTimer = null;
		}

		// 清理购物车数据获取防抖定时器
		if (this.getCartTimer) {
			clearTimeout(this.getCartTimer);
			this.getCartTimer = null;
		}

		// 清理待处理的请求
		this.pendingUpdateRequests.clear();

		// 重置状态
		this.isUpdatingCart = false;
		this.disabledNumberInput = false;
	},

	onShareAppMessage(options) {
		return {
			title: this.$store.state.baseSet.shop,
			path: '/pages/index/index?businessmanId=' + (this.$store.state.userStatus.id||''),
				success: res => {
			}
		};
	},
	// #ifdef MP-WEIXIN
	// 分享到朋友圈
	onShareTimeline(obj) {},
	// 收藏小程序
	onAddToFavorites() {}
	// #endif
};
</script>

<style lang="scss">
.box {
	border-bottom: 1px solid #eeeeee;
	.title {
		font-size: 28upx;
		font-weight: 510;
		padding-left: 24upx;
		line-height: 94rpx;
		color: #3c3c3c;
	}
	.ibonfont {
		display: inline-block;
		color: #cccccc;
		font-size: 36rpx;
	}
}
.container {
	padding-bottom: 100upx;
}

/* 购物车列表项 */
.card {
	background: #ffffff;
	padding: 0 48upx 32rpx;
	margin-bottom: 20rpx;
}
.total-view {
	text-align: right;
	font-size: 32upx;
	color: #999;
	padding: 24upx 0;

	.price {
		color: $price-color;
		font-size: 30upx;
		font-weight: 510;
	}
}

.uniSwipe:last-child {
	.cart-item {
		border-bottom: 0 none;
	}
}


.cart-item {
	padding-top: 24upx;
	.ibonfont {
		transform: translateY(60upx);
		color: #cccccc;
		font-size: 36rpx;
		display: inline-block;
		width: 34rpx;
		height: 34rpx;
		margin-right: 16upx;
	}
	.image-wrapper {
		width: 152upx;
		height: 152upx;

		background-color: #eef3f4;
		border-radius: 12upx;
		image {
			width: 100%;
			height: 100%;
			border-radius: 12upx;
		}
	}
	.item-right {
		margin-left: 16upx;
		width: 430rpx;
		position: relative;
		.del-btn {
			padding: 4upx 10upx;
			font-size: 30upx;
			height: 50upx;
			color: $font-color-light;
			position: absolute;
			top: 0;
			right: 20upx;
		}
		.title {
			color: #3c3c3c;
			font-size: 28upx;
			height: 40upx;
			line-height: 40upx;
			text-overflow: -o-ellipsis-lastline;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			line-clamp: 2;
			-webkit-box-orient: vertical;
			font-weight: 510;
		}
		.attr {
			font-size: 24upx;
			font-weight: 400;
			color: #9b9b9b;
			height: 60rpx;
			margin: 8rpx 0;
			line-height: 32rpx;
		}
		.price {
			color: $price-color;
			font-size: 40upx;
			font-weight: 510;
			font-family: DIN-Medium;
		}
		.activity-tag {
			width: 76rpx;
			font-size: 20rpx;
			color: #ffffff;
			height: 32rpx;
			line-height: 32rpx;
			text-align: center;
			border-radius: 6rpx;
			margin-left: 10rpx;
			margin-top: 16rpx;
		}
	}
}
/* 底部栏 */
.action-section {
	position: fixed;
	left: 0upx;
	bottom: calc(env(safe-area-inset-bottom) + 100upx);
	z-index: 95;
	width: 100%;
	height: 100rpx;
	background-color: #ffffff;
	.delivery-tip {
		font-size: 22upx;
		background-color: #fdf6ec;
		color: #d48d24;
		text-align: center;
		line-height: 42upx;
	}
	.action-section-main {
		padding: 0 0 0 32upx;
		height: 100upx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.checkbox {
			height: 52upx;
			image {
				width: 30upx;
				height: 100%;
				z-index: 5;
			}
			.ibonfont {
				font-size: 36rpx;
				display: inline-block;
				width: 34rpx;
				height: 34rpx;
				color: #cccccc;
				transform: translateY(10rpx);
			}
		}
		.all-btn {
			color: #6c6c6c;
			font-size: 24upx;
			line-height: 60upx;
			padding-left: 12upx;
			font-weight: 400;
		}
		.total-box {
			flex: 1;
			text-align: right;
			.total-text {
				color: #3c3c3c;
				font-size: 24upx;
				font-weight: 400;
			}
			.price {
				font-size: 36upx;
				color: $price-color;
				font-weight: 600;
			}
			.freeExpressPrice {
				font-size: 20rpx;
				color: #9c9c9c;
			}
		}
		.confirm-btn {
			height: 52upx;
			line-height: 52upx;
			color: #ffffff;
			background-color: #f63434;
			text-align: center;
			border-radius: 52upx;
			padding: 0 30rpx;
			margin: 0 24rpx;
			font-size: 28rpx;
		}
		.diff-price {
			font-size: 24upx;
			color: #666;
			line-height: 90upx;
			padding-right: 10upx;
		}

		.del-goods-btn {
			width: 140upx;
			height: 60rpx;
			line-height: 60rpx;
			border: 1px solid #fd463e;
			color: #fd463e;
			font-size: 26upx;
			text-align: center;
			border-radius: 6upx;
			margin-right: 24upx;
		}
	}
}

/* 复选框选中状态 */
.action-section .checkbox.checked,
.cart-item .checkbox.checked {
	color: #fd463e;
}

.invalid-card {
	background: #ffffff;
	border-radius: 12upx;
	margin: 0 auto 24upx;
	padding: 0 24rpx 24rpx;
	width: 702rpx;
	.invalid-view {
		line-height: 94rpx;
		border-bottom: 1px solid #eeeeee;
		.invalid-tit {
			font-size: 28upx;
			font-weight: 510;
			color: #3c3c3c;
		}

		.invalid-btn {
			font-size: 24upx;
			color: $uni-color-error;
		}
	}
	.cart-list {
		.cart-item {
			.image-wrapper {
				position: relative;
				.invalid-tag {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 100rpx;
					height: 100rpx;
					background-color: rgba($color: #000000, $alpha: 0.5);
					font-size: 20rpx;
					color: #eeeeee;
					font-weight: 300;
					border-radius: 100%;
					text {
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						text-align: center;
						display: block;
						width: 88rpx;
						height: 88rpx;
						line-height: 90rpx;
						border-radius: 100%;
						border: 2rpx solid #cccccc;
					}
				}
			}
			.item-right {
				position: relative;
				height: 152rpx;
				.title {
					height: 66rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #9b9b9b;
					line-height: 33rpx;
				}
				.invalid-msg {
					position: absolute;
					left: 0;
					bottom: 0;
					font-size: 24rpx;
					font-weight: 400;
					color: #3c3c3c;
				}
			}
		}
	}
}

.full-give-list {
	position: fixed;
	bottom: calc(env(safe-area-inset-bottom) + 192rpx);
	white-space: nowrap;
	width: 100%;
	height: 240rpx;  /* 增加明确的高度 */
	z-index: 10;     /* 增加z-index确保不被其他元素遮挡 */
	background-color: rgba(255, 255, 255, 0.9); /* 添加背景色 */
	padding: 10rpx 0; /* 添加内边距 */
	display: flex;    /* 使用flex布局 */
	overflow-x: auto; /* 允许水平滚动 */
	
	.full-give-loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		
		text {
			font-size: 24rpx;
			color: #666;
			margin-top: 10rpx;
		}
	}
	
	.full-give-item {
		width: 200rpx;
		height: 220rpx;
		text-align: center;
		display: inline-block;
		margin: 0 5rpx;
		flex-shrink: 0;  /* 防止flex项目被压缩 */
		border-radius: 14px; /* 整体圆角 */
		overflow: hidden;   /* 处理溢出 */
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1); /* 添加阴影 */
		
		.full-give-price {
			background-color: var(--primary-color, red); /* 使用变量颜色 */
			border-top-left-radius: 14px;
			border-top-right-radius: 14px;
			padding: 8rpx 0;
			
			.full-give-full {
				font-size: 28rpx;
				color: #FEF00A;
				padding: 0 5rpx;
				white-space: nowrap;  /* 防止文字换行 */
				overflow: hidden;     /* 处理溢出 */
				text-overflow: ellipsis; /* 文字溢出显示省略号 */
			}
			
			.full-give-balance {
				font-size: 24rpx;
				background-color: #FEF00A;
				margin: 0 10rpx;
				padding: 2rpx 5rpx;
				border-radius: 6rpx;
				white-space: nowrap;  /* 防止文字换行 */
				overflow: hidden;     /* 处理溢出 */
				text-overflow: ellipsis; /* 文字溢出显示省略号 */
			}
		}
		
		.full-give-img {
			width: 100%;
			height: 160rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #fff;
			
			.full-give-img-one {
				width: 160rpx;
				height: 160rpx;
				object-fit: cover; /* 图片合适填充 */
			}
		}
	}
}

.full-give-title {
	position: relative;
	padding: 30rpx 40rpx;
	border-bottom: 2rpx solid #f5f5f5;
	display: flex;
	justify-content: center;
	align-items: center;
	background: linear-gradient(to right, var(--primary-color), var(--primary-light-color));
	border-radius: 24rpx 24rpx 0 0;
	
	text {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		
		&.full-give-close {
			position: absolute;
			right: 30rpx;
			font-size: 28rpx;
			padding: 10rpx;
			opacity: 0.8;
			transition: all 0.3s;
			
			&:active {
				opacity: 1;
				transform: scale(1.1);
			}
		}
	}
}

.full-give-goods-scroll {
	height: calc(100% - 100rpx);
	background: #f8f8f8;
}

.full-give-goods-list {
	padding: 20rpx;
	
	.full-give-level-info {
		margin: 20rpx;
		padding: 30rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		
		.level-title {
			font-size: 30rpx;
			color: #333;
			font-weight: bold;
			margin-bottom: 16rpx;
			display: block;
		}
		
		.level-desc {
			font-size: 26rpx;
			color: #666;
			line-height: 1.5;
		}
	}
}

.full-give-goods-item {
	display: flex;
	align-items: center;
	margin: 20rpx;
	padding: 20rpx;
	background: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s;
	
	&:active {
		transform: scale(0.98);
	}
	
	.full-give-goods-image {
		width: 180rpx;
		height: 180rpx;
		margin-right: 24rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background: #f5f5f5;
		
		image {
			width: 100%;
			height: 100%;
			transition: all 0.3s;
			
			&:active {
				transform: scale(1.05);
			}
		}
	}
	
	.full-give-goods-info {
		flex: 1;
		
		.full-give-goods-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 12rpx;
			line-height: 1.4;
		}
		
		.full-give-goods-spec {
			font-size: 24rpx;
			color: #666;
			margin: 12rpx 0;
			line-height: 1.4;
		}
		
		.full-give-goods-stock {
			font-size: 24rpx;
			color: #999;
			display: flex;
			align-items: center;
			
			&::before {
				content: '';
				display: inline-block;
				width: 12rpx;
				height: 12rpx;
				background: var(--primary-color);
				border-radius: 50%;
				margin-right: 8rpx;
			}
		}
	}
	
	.full-give-goods-action {
		.full-give-goods-btn {
			padding: 12rpx 36rpx;
			background: var(--primary-color);
			color: #fff;
			border-radius: 32rpx;
			font-size: 26rpx;
			transition: all 0.3s;
			
			&:active {
				transform: scale(0.95);
				opacity: 0.9;
			}
		}
	}
}

.full-give-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
	
	.u-loading {
		margin-bottom: 20rpx;
	}
	
	text {
		font-size: 28rpx;
		color: #999;
		letter-spacing: 2rpx;
	}
}

.full-give-empty {
	text-align: center;
	padding: 80rpx 40rpx;
	
	text {
		color: #999;
		font-size: 28rpx;
		line-height: 1.5;
	}
	
	.retry-btn {
		display: inline-block;
		margin-top: 30rpx;
		padding: 16rpx 40rpx;
		background: var(--primary-color);
		color: #fff;
		border-radius: 32rpx;
		font-size: 26rpx;
		transition: all 0.3s;
		
		&:active {
			transform: scale(0.95);
			opacity: 0.9;
		}
	}
}

.full-buy-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 27rpx;
	padding-left: 40rpx;

	.full-buy-info-left {
		display: flex;
		align-items: center;
		justify-content: space-between;
		.full-buy-brand {
			padding: 15rpx 7rpx;
		}
		.full-buy-msg {
			.full-buy-price {
				padding: 0 5rpx;
				font-size: inherit;
			}
		}
		.full-buy-countdown {
			font-size: 20rpx;
			color: #9b9b9b;
		}
	}
	.full-buy-btn {
	}
}
.full-buy-brand {
	margin: 5rpx 10rpx;
	padding: 2rpx 7rpx;
	border-radius: 10rpx;
	border-width: 1px;
	border-style: solid;
	font-size: 20rpx;
}
.full-buy-title {
	position: sticky;
	top: 0;
	z-index: 1;
	background-color: #fff;
	padding: 30rpx 20rpx;
	text-align: center;
	font-size: 34rpx;
	font-weight: bold;

	.full-buy-close {
		position: absolute;
		right: 20rpx;
	}
}

.full-buy-goods-item {
	padding: 24rpx 40rpx 0 40rpx;
	.full-buy-goods-item-right {
		width: calc(100% - 152upx - 16upx);
	}
	.full-buy-goods-item-btn {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}

.limit-quantity {
	padding-top: 10rpx;
	color: #fa3534;
}

.full-buy-footer {
	position: fixed;
	bottom: 0;
	width: 100%;
	padding: 20rpx 40rpx;
	padding-bottom: calc(env(safe-area-inset-bottom));
	background-color: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	.full-buy-footer-btn {
		width: 100rpx;
		height: 60rpx;
		line-height: 60rpx;
		border-radius: 6rpx;
		text-align: center;
		color: #fff;
		border-radius: 60rpx;
	}
}

.full-give-info {
	margin-top: 10rpx;
	margin-bottom: 10rpx;
	
	.full-give-btn {
		display: inline-block;
		font-size: 24rpx;
		padding: 8rpx 20rpx;
		border-radius: 30rpx;
		border: 1px solid var(--primary-color);
		color: var(--primary-color);
		background-color: #fff;
		
		&.primary-bg {
			color: #fff;
			background-color: var(--primary-color);
			border: 1px solid var(--primary-color);
		}
		
		.full-give-tip {
			margin-left: 6rpx;
		}
	}
}

.full-give-title {
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	.full-give-close {
		color: #999;
		font-size: 28rpx;
	}
}

.full-give-goods-scroll {
	height: calc(100% - 100rpx);
}

.full-give-goods-list {
	padding: 20rpx;
	
	.full-give-level-info {
		margin-bottom: 20rpx;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		
		.level-title {
			font-size: 28rpx;
			color: #333;
			font-weight: bold;
			margin-bottom: 10rpx;
			display: block;
		}
		
		.level-desc {
			font-size: 24rpx;
			color: #666;
		}
	}
}

.full-give-goods-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
	
	&:last-child {
		border-bottom: none;
	}
	
	.full-give-goods-image {
		width: 160rpx;
		height: 160rpx;
		margin-right: 20rpx;
		
		image {
			width: 100%;
			height: 100%;
			border-radius: 8rpx;
		}
	}
	
	.full-give-goods-info {
		flex: 1;
		
		.full-give-goods-title {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 10rpx;
		}
		
		.full-give-goods-spec {
			font-size: 24rpx;
			color: #666;
			margin-bottom: 10rpx;
		}
		
		.full-give-goods-stock {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.full-give-goods-action {
		.full-give-goods-btn {
			padding: 10rpx 30rpx;
			background-color: var(--primary-color);
			color: #fd463e;
			border-radius: 30rpx;
			font-size: 24rpx;
        
            &.disabled-btn {
                background-color: #ccc;
                color: #999;
                pointer-events: none;
            }
		}
	}
}

.full-give-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	
	text {
		font-size: 26rpx;
		color: #999;
		margin-top: 20rpx;
	}
}

.full-give-empty {
	text-align: center;
	padding: 40rpx 0;
	color: #999;
	font-size: 28rpx;
}

.full-give-info {
	.u-loading {
		margin-right: 10rpx;
		display: inline-block;
		vertical-align: middle;
	}
}

/* 商品满赠按钮 */
.full-give-btn {
	padding: 4rpx 10rpx;
	font-size: 20rpx;
	color: var(--primary-color);
	border: 1px solid var(--primary-color);
	border-radius: 20rpx;
	margin-left: 10rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	&.primary-bg {
		color: #ffffff;
		border: none;
	}
	
	.full-give-text {
		font-size: 20rpx;
		color: #e02020;
		line-height: 1;
	}

	.full-give-tip {
		font-size: 20rpx;
		color: #e02020;
		line-height: 1;
	}
}

/* 倍数赠送提示 */
.multiple-gift-tip {
	font-size: 18rpx;
	color: #ff6600;
	margin-left: 8rpx;
}

/* 倍数赠品数量提示 */
.multiple-gift-quantity {
	font-size: 20rpx;
	color: #ff6600;
	margin-bottom: 8rpx;
	text-align: center;
}

/* 剩余赠品数量提示 */
.remaining-gift-tip {
	font-size: 18rpx;
	color: #999999;
	margin-left: 8rpx;
}

/* 未满足条件提示 */
.full-give-not-meet {
	text-align: center;
	padding: 40rpx 0;
	
	text {
		color: #666;
		font-size: 28rpx;
	}
	
	.add-more-btn {
		display: inline-block;
		margin-top: 20rpx;
		padding: 16rpx 40rpx;
		color: #fff;
		border-radius: 32rpx;
		font-size: 26rpx;
	}
}

.full-give-title {
	display: flex;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid #eee;
	
	text {
		font-size: 30rpx;
		font-weight: bold;
	}
	
	.full-give-close {
		font-weight: normal;
		color: #666;
	}
}

.full-give-goods-scroll {
	height: 680rpx;
}

.full-give-level-info {
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f2f2f2;
	
	.level-title {
		font-size: 28rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.level-desc {
		font-size: 24rpx;
		color: #666;
	}
}

.full-give-goods-item {
	display: flex;
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #f2f2f2;
	
	.full-give-goods-image {
		width: 150rpx;
		height: 150rpx;
		border-radius: 8rpx;
		overflow: hidden;
		
		image {
			width: 100%;
			height: 100%;
		}
	}
	
	.full-give-goods-info {
		flex: 1;
		padding: 0 20rpx;
		
		.full-give-goods-title {
			font-size: 28rpx;
			margin-bottom: 10rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		
		.full-give-goods-spec {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.full-give-goods-action {
		display: flex;
		align-items: center;
		
		.full-give-goods-btn {
			padding: 10rpx 30rpx;
			border-radius: 30rpx;
			color: #fff;
			font-size: 24rpx;
		}
	}
}

.full-give-empty {
	text-align: center;
	padding: 40rpx 0;
	color: #999;
	font-size: 28rpx;
}
</style>
